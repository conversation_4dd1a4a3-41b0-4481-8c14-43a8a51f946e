#!/bin/bash

# Unity MCP 完整启动脚本（包含模拟器）

echo "🎮 启动完整的 Unity MCP 环境..."

# 检查依赖
if [ ! -f "Unity-MCP/Server~/build/index.js" ]; then
    echo "❌ Unity MCP 服务器未构建"
    exit 1
fi

if [ ! -f "unity-simulator.js" ]; then
    echo "❌ Unity 模拟器文件不存在"
    exit 1
fi

# 停止现有进程
echo "🛑 停止现有进程..."
pkill -f "unity-simulator" 2>/dev/null
pkill -f "Unity-MCP" 2>/dev/null
sleep 2

# 启动 Unity WebSocket 模拟器
echo "1. 启动 Unity WebSocket 模拟器..."
node unity-simulator.js &
SIMULATOR_PID=$!
sleep 3

# 启动 Unity MCP 服务器
echo "2. 启动 Unity MCP 服务器..."
./start-unity-local.sh &
MCP_PID=$!
sleep 3

# 检查服务状态
echo ""
echo "🔍 检查服务状态..."

# 检查模拟器
if curl -s http://localhost:8091/status >/dev/null; then
    echo "✅ Unity 模拟器运行正常"
else
    echo "❌ Unity 模拟器启动失败"
fi

# 检查 MCP 服务器
if curl -s http://localhost:8940/health >/dev/null; then
    echo "✅ Unity MCP 服务器运行正常"
else
    echo "❌ Unity MCP 服务器启动失败"
fi

echo ""
echo "🎉 Unity MCP 环境启动完成！"
echo ""
echo "📋 服务信息:"
echo "   - Unity 模拟器: ws://localhost:8090"
echo "   - Unity MCP: http://localhost:8940/sse"
echo "   - 状态检查: http://localhost:8091/status"
echo ""
echo "🔧 Cursor 配置:"
echo '   {"mcpServers": {"unity-mcp": {"url": "http://localhost:8940/sse"}}}'
echo ""
echo "💡 使用说明:"
echo "1. 在 Cursor 中添加上述配置"
echo "2. 重启 Cursor"
echo "3. 测试: @cursor 使用 Unity MCP 获取场景信息"
echo ""
echo "按 Ctrl+C 停止所有服务..."

# 等待中断信号
trap 'echo "🛑 停止所有服务..."; kill $SIMULATOR_PID $MCP_PID 2>/dev/null; exit 0' INT

wait $MCP_PID
