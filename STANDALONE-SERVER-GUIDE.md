# 🌐 Playwright MCP 独立服务器配置指南

## 🎯 概述

独立服务器模式允许您将 Playwright MCP 作为独立的 HTTP 服务运行，任何支持 MCP 协议的客户端都可以通过 SSE (Server-Sent Events) 端点连接使用。

## 🚀 启动服务器

### 方法一：使用启动脚本（推荐）

我为您准备了三个不同模式的启动脚本：

```bash
# 标准模式（有头浏览器）- 端口 8931
./start-standalone-server.sh

# 无头模式（后台运行）- 端口 8932  
./start-headless-server.sh

# 视觉模式（支持坐标点击）- 端口 8933
./start-vision-server.sh
```

### 方法二：手动启动

```bash
# 基础独立服务器
npx @playwright/mcp@latest --port 8931

# 无头模式
npx @playwright/mcp@latest --port 8932 --headless

# 视觉模式
npx @playwright/mcp@latest --port 8933 --vision

# 使用配置文件
npx @playwright/mcp@latest --port 8931 --config standalone-server-config.json
```

## 🔗 客户端连接配置

### 通用 SSE 端点

启动服务器后，客户端可以通过以下端点连接：

- **标准模式**: `http://localhost:8931/sse`
- **无头模式**: `http://localhost:8932/sse`  
- **视觉模式**: `http://localhost:8933/sse`

### VS Code 配置

在 VS Code 的 `settings.json` 中添加：

```json
{
  "mcp.servers": {
    "playwright-standalone": {
      "url": "http://localhost:8931/sse"
    }
  }
}
```

### Claude Desktop 配置

在 Claude Desktop 配置文件中添加：

```json
{
  "mcpServers": {
    "playwright-standalone": {
      "url": "http://localhost:8931/sse"
    }
  }
}
```

### Cursor 配置

在 Cursor Settings -> MCP 中添加：
- Name: `playwright-standalone`
- Type: `URL`
- URL: `http://localhost:8931/sse`

### Windsurf 配置

按照 Windsurf MCP 文档，使用以下配置：

```json
{
  "mcpServers": {
    "playwright-standalone": {
      "url": "http://localhost:8931/sse"
    }
  }
}
```

## 🔧 服务器配置选项

### 端口和主机

```bash
# 自定义端口
npx @playwright/mcp@latest --port 9000

# 绑定到所有接口（允许远程访问）
npx @playwright/mcp@latest --port 8931 --host 0.0.0.0

# 仅本地访问
npx @playwright/mcp@latest --port 8931 --host localhost
```

### 浏览器选项

```bash
# 选择浏览器
npx @playwright/mcp@latest --port 8931 --browser chrome
npx @playwright/mcp@latest --port 8931 --browser firefox
npx @playwright/mcp@latest --port 8931 --browser webkit

# 无头模式
npx @playwright/mcp@latest --port 8931 --headless

# 视觉模式
npx @playwright/mcp@latest --port 8931 --vision
```

### 高级选项

```bash
# 完整配置示例
npx @playwright/mcp@latest \
  --port 8931 \
  --host 0.0.0.0 \
  --browser chrome \
  --output-dir ./playwright-output \
  --save-trace \
  --viewport-size "1920,1080" \
  --user-agent "Mozilla/5.0 (compatible; MCP Bot)" \
  --ignore-https-errors
```

## 🐳 Docker 部署

### 使用官方 Docker 镜像

```bash
# 拉取并运行官方镜像
docker run -p 8931:8931 mcr.microsoft.com/playwright/mcp

# 客户端配置
{
  "mcpServers": {
    "playwright-docker": {
      "url": "http://localhost:8931/sse"
    }
  }
}
```

### 自定义 Dockerfile

```dockerfile
FROM mcr.microsoft.com/playwright:v1.53.0-focal

WORKDIR /app
RUN npm install -g @playwright/mcp@latest

EXPOSE 8931

CMD ["npx", "@playwright/mcp@latest", "--port", "8931", "--host", "0.0.0.0", "--headless"]
```

## 🔍 验证和测试

### 检查服务器状态

```bash
# 检查端口是否监听
lsof -i :8931

# 测试 SSE 端点
curl -N http://localhost:8931/sse
```

### 测试连接

```bash
# 使用 curl 测试
curl -H "Accept: text/event-stream" http://localhost:8931/sse
```

## 🛠️ 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查找占用端口的进程
   lsof -i :8931
   
   # 终止进程
   kill -9 <PID>
   ```

2. **浏览器启动失败**
   ```bash
   # 添加 --no-sandbox 参数
   npx @playwright/mcp@latest --port 8931 --no-sandbox
   ```

3. **权限问题**
   ```bash
   # 确保有执行权限
   chmod +x start-standalone-server.sh
   ```

4. **网络访问问题**
   ```bash
   # 检查防火墙设置
   sudo ufw allow 8931
   ```

## 🌟 优势

### 独立服务器模式的优势：

1. **跨客户端兼容**: 任何支持 MCP 的客户端都可以使用
2. **集中管理**: 一个服务器实例服务多个客户端
3. **资源共享**: 多个客户端共享同一个浏览器实例
4. **远程访问**: 可以部署在服务器上供远程使用
5. **容器化**: 支持 Docker 部署

### 适用场景：

- 团队协作环境
- 服务器部署
- 多客户端使用
- 资源受限环境
- 生产环境部署

## 📊 监控和日志

### 查看服务器日志

服务器启动后会显示详细的日志信息，包括：
- 连接状态
- 请求处理
- 错误信息
- 性能指标

### 输出文件

所有生成的文件（截图、PDF、跟踪文件）都会保存在 `./playwright-output` 目录中。

## 🎉 开始使用

1. **启动服务器**: 运行相应的启动脚本
2. **配置客户端**: 在您的 MCP 客户端中添加 SSE 端点
3. **重启客户端**: 重启客户端以加载新配置
4. **开始自动化**: 享受强大的浏览器自动化功能！

现在您可以在任何支持 MCP 的客户端中使用 Playwright 功能了！🚀
