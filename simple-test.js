#!/usr/bin/env node

/**
 * 简单测试 Playwright MCP 服务器
 */

console.log('🚀 Playwright MCP 服务器配置测试\n');

console.log('✅ Node.js 版本:', process.version);
console.log('✅ 当前目录:', process.cwd());

// 检查配置文件
const fs = require('fs');
const path = require('path');

const configFiles = [
  'mcp-config.json',
  'mcp-config-advanced.json', 
  'playwright-mcp-config.json'
];

console.log('\n📁 配置文件检查:');
configFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - 存在`);
    try {
      const config = JSON.parse(fs.readFileSync(file, 'utf8'));
      console.log(`   📋 配置有效`);
    } catch (e) {
      console.log(`   ❌ 配置格式错误: ${e.message}`);
    }
  } else {
    console.log(`❌ ${file} - 不存在`);
  }
});

console.log('\n🎉 配置检查完成！');
console.log('\n📋 下一步操作：');
console.log('1. 选择合适的配置文件（基础版或高级版）');
console.log('2. 将配置添加到您的 MCP 客户端');
console.log('3. 重启 MCP 客户端');
console.log('4. 开始使用 Playwright 工具！');

console.log('\n🔧 可用配置：');
console.log('• mcp-config.json - 基础配置');
console.log('• mcp-config-advanced.json - 多实例配置');
console.log('• playwright-mcp-config.json - 详细配置选项');
