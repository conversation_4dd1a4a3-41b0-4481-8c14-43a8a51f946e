#!/usr/bin/env node

/**
 * 测试 Unity MCP 连接
 */

const { spawn } = require('child_process');
const WebSocket = require('ws');

console.log('🧪 测试 Unity MCP 连接...');

// 1. 测试 Unity WebSocket 连接
function testUnityWebSocket() {
    return new Promise((resolve, reject) => {
        console.log('1. 测试 Unity WebSocket (端口 8090)...');
        
        const ws = new WebSocket('ws://localhost:8090');
        
        ws.on('open', () => {
            console.log('✅ Unity WebSocket 连接成功');
            ws.send(JSON.stringify({ type: 'test', message: 'Hello Unity' }));
        });
        
        ws.on('message', (data) => {
            console.log('📨 收到 Unity 响应:', data.toString());
            ws.close();
            resolve(true);
        });
        
        ws.on('error', (error) => {
            console.log('❌ Unity WebSocket 连接失败:', error.message);
            resolve(false);
        });
        
        ws.on('close', () => {
            console.log('🔌 Unity WebSocket 连接已关闭');
        });
        
        // 超时处理
        setTimeout(() => {
            if (ws.readyState === WebSocket.CONNECTING) {
                console.log('⏰ Unity WebSocket 连接超时');
                ws.close();
                resolve(false);
            }
        }, 5000);
    });
}

// 2. 测试 Unity MCP 服务器
function testUnityMCP() {
    return new Promise((resolve, reject) => {
        console.log('2. 测试 Unity MCP 服务器...');
        
        const mcp = spawn('node', ['/root/Desktop/MCP/Unity-MCP/Server~/build/index.js'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: {
                ...process.env,
                UNITY_PORT: '8090',
                UNITY_HOST: 'localhost',
                UNITY_REQUEST_TIMEOUT: '10',
                LOGGING: 'true'
            }
        });
        
        let output = '';
        
        mcp.stdout.on('data', (data) => {
            output += data.toString();
            console.log('📤 MCP 输出:', data.toString().trim());
        });
        
        mcp.stderr.on('data', (data) => {
            console.log('❌ MCP 错误:', data.toString().trim());
        });
        
        mcp.on('close', (code) => {
            console.log(`🔚 MCP 进程退出，代码: ${code}`);
            resolve(code === 0);
        });
        
        // 发送测试消息
        setTimeout(() => {
            const testMessage = {
                jsonrpc: '2.0',
                id: 1,
                method: 'initialize',
                params: {
                    protocolVersion: '2024-11-05',
                    capabilities: {},
                    clientInfo: {
                        name: 'test-client',
                        version: '1.0.0'
                    }
                }
            };
            
            console.log('📨 发送测试消息到 MCP...');
            mcp.stdin.write(JSON.stringify(testMessage) + '\n');
        }, 1000);
        
        // 超时处理
        setTimeout(() => {
            console.log('⏰ MCP 测试超时');
            mcp.kill();
            resolve(false);
        }, 10000);
    });
}

// 主测试函数
async function runTests() {
    console.log('🚀 开始测试 Unity MCP 环境...\n');
    
    // 测试 Unity WebSocket
    const unityOk = await testUnityWebSocket();
    console.log('');
    
    // 测试 Unity MCP
    const mcpOk = await testUnityMCP();
    console.log('');
    
    // 总结
    console.log('📊 测试结果:');
    console.log(`   Unity WebSocket: ${unityOk ? '✅ 正常' : '❌ 失败'}`);
    console.log(`   Unity MCP: ${mcpOk ? '✅ 正常' : '❌ 失败'}`);
    console.log('');
    
    if (unityOk && mcpOk) {
        console.log('🎉 Unity MCP 环境测试通过！');
        console.log('');
        console.log('🔧 Cursor 配置:');
        console.log(JSON.stringify({
            "mcpServers": {
                "unity-mcp": {
                    "command": "node",
                    "args": ["/root/Desktop/MCP/Unity-MCP/Server~/build/index.js"],
                    "env": {
                        "UNITY_PORT": "8090",
                        "UNITY_HOST": "localhost",
                        "UNITY_REQUEST_TIMEOUT": "10",
                        "LOGGING": "true"
                    }
                }
            }
        }, null, 2));
    } else {
        console.log('❌ Unity MCP 环境测试失败！');
        console.log('');
        console.log('🔧 故障排除建议:');
        if (!unityOk) {
            console.log('   - 启动 Unity 模拟器: node unity-simulator.js');
            console.log('   - 或启动远程服务器: ./start-unity-remote.sh');
        }
        if (!mcpOk) {
            console.log('   - 检查 Unity MCP 构建: ls -la Unity-MCP/Server~/build/');
            console.log('   - 检查依赖: cd Unity-MCP/Server~ && npm install');
        }
    }
}

// 运行测试
runTests().catch(console.error);
