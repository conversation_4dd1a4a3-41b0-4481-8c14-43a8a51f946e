# 🎮 Cursor 中使用 Unity MCP 完整指南

## 🎉 安装状态：完成！

Unity MCP 环境已成功安装并运行，包含：
- ✅ Unity WebSocket 模拟器 (端口 8090)
- ✅ Unity MCP 服务器 (端口 8940)
- ✅ 完整的模拟 Unity 环境

## 🔧 在 Cursor 中配置

### 方法 1：直接命令模式（推荐）

在 Cursor 的 MCP 设置中添加：

```json
{
  "mcpServers": {
    "unity-mcp": {
      "command": "node",
      "args": [
        "/root/Desktop/MCP/Unity-MCP/Server~/build/index.js"
      ],
      "env": {
        "UNITY_PORT": "8090",
        "UNITY_HOST": "localhost",
        "UNITY_REQUEST_TIMEOUT": "10",
        "LOGGING": "true"
      }
    }
  }
}
```

### 方法 2：独立服务器模式

```json
{
  "mcpServers": {
    "unity-mcp": {
      "url": "http://localhost:8940/sse"
    }
  }
}
```

## 🚀 启动服务

### 自动启动（推荐）
```bash
cd /root/Desktop/MCP
./start-unity-complete.sh
```

### 手动启动
```bash
# 1. 启动 Unity 模拟器
node unity-simulator.js &

# 2. 启动 Unity MCP 服务器
./start-unity-local.sh &
```

## 🧪 测试功能

### 1. 验证服务状态
```bash
# Unity 模拟器状态
curl http://localhost:8091/status

# Unity MCP 服务器状态
curl http://localhost:8940/health
```

### 2. 在 Cursor 中测试

配置完成后，在 Cursor 中尝试：

```
@cursor 你现在有哪些工具可用？

@cursor 使用 Unity MCP 获取场景层级结构

@cursor 使用 Unity MCP 创建一个新的游戏对象

@cursor 使用 Unity MCP 获取所有游戏对象信息

@cursor 使用 Unity MCP 查看 Unity 控制台日志
```

## 🎯 可用功能

### Unity MCP 工具
- **execute_menu_item** - 执行 Unity 菜单项
- **select_gameobject** - 选择游戏对象
- **update_gameobject** - 更新游戏对象属性
- **update_component** - 更新组件属性
- **add_package** - 添加 Unity 包
- **run_tests** - 运行测试
- **send_console_log** - 发送控制台日志
- **add_asset_to_scene** - 添加资源到场景

### Unity MCP 资源
- **unity://menu-items** - 获取所有可用菜单项
- **unity://scenes-hierarchy** - 获取场景层级结构
- **unity://gameobject/{id}** - 获取游戏对象详细信息
- **unity://logs** - 获取 Unity 控制台日志
- **unity://packages** - 获取已安装包信息
- **unity://assets** - 获取项目资源信息

## 📊 模拟环境数据

当前模拟器包含：
- **1个场景**: SampleScene
- **2个游戏对象**: Main Camera, Directional Light
- **2个包**: Unity UI, TextMeshPro
- **实时日志**: 系统消息和操作记录

## 🔍 使用示例

### 基础操作
```
@cursor 使用 Unity MCP 帮我：
1. 获取当前场景的所有游戏对象
2. 创建一个名为 "Player" 的新游戏对象
3. 查看 Unity 控制台的最新日志
```

### 高级操作
```
@cursor 使用 Unity MCP 执行以下操作：
1. 获取场景层级结构
2. 选择 Main Camera 对象
3. 为 Player 对象添加 Rigidbody 组件
4. 执行菜单项创建一个 Cube
```

## 🚨 故障排除

### 常见问题

#### 1. Cursor 中看不到 Unity 工具
**解决方案**：
- 确认配置格式正确
- 重启 Cursor
- 检查服务器是否运行

#### 2. 连接失败
**解决方案**：
```bash
# 检查服务状态
curl http://localhost:8940/health
curl http://localhost:8091/status

# 重启服务
./start-unity-complete.sh
```

#### 3. 工具响应异常
**解决方案**：
- 查看服务器日志
- 重启 Unity 模拟器
- 检查端口占用

### 调试命令
```bash
# 查看进程
ps aux | grep -E "(unity|node)"

# 查看端口
lsof -i :8090
lsof -i :8940

# 重启服务
pkill -f unity && ./start-unity-complete.sh
```

## 📈 扩展功能

### 添加更多模拟数据
编辑 `unity-simulator.js` 文件，可以：
- 添加更多游戏对象
- 模拟更复杂的场景
- 增加更多 Unity 包
- 自定义响应逻辑

### 连接真实 Unity Editor
如果您安装了真实的 Unity Editor：
1. 停止模拟器
2. 在 Unity Editor 中安装 MCP 插件
3. 配置 WebSocket 连接到 localhost:8090
4. 重启 Unity MCP 服务器

## 🎉 成功标志

配置成功后，您应该能够：
- ✅ 在 Cursor 中看到 Unity MCP 工具
- ✅ 获取模拟的 Unity 场景信息
- ✅ 创建和操作游戏对象
- ✅ 查看模拟的 Unity 日志
- ✅ 执行各种 Unity 操作

现在您可以在 Cursor 中享受完整的 Unity MCP 功能了！🚀
