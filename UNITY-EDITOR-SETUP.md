# 🎮 Unity Editor 本地配置指南

## 📋 概述

本指南将帮助您在本地 Unity Editor 中配置 MCP 插件，以连接到远程服务器上的 Unity MCP 服务。

## 🎯 架构说明

```
本地 Unity Editor          远程服务器
┌─────────────────┐         ┌──────────────────┐
│   Unity Editor  │ WebSocket│  Unity MCP       │
│   + MCP 插件    │ ────────→│  Node.js 服务器  │
│                 │  :8090   │                  │
└─────────────────┘         └──────────────────┘
```

## 📦 Unity Editor 安装

### 1. 下载和安装 Unity

1. **下载 Unity Hub**：
   - 访问：https://unity3d.com/get-unity/download
   - 下载并安装 Unity Hub

2. **安装 Unity Editor**：
   - 打开 Unity Hub
   - 点击 "Installs" 标签
   - 点击 "Install Editor"
   - 选择 Unity 2022.3 LTS 或更新版本
   - 确保勾选必要的模块

### 2. 创建或打开 Unity 项目

1. **创建新项目**：
   - 在 Unity Hub 中点击 "New project"
   - 选择 3D 模板
   - 命名项目并选择位置
   - 点击 "Create project"

2. **或打开现有项目**：
   - 在 Unity Hub 中点击 "Open"
   - 选择您的项目文件夹

## 🔌 安装 Unity MCP 插件

### 方法一：从服务器复制文件

1. **下载插件文件**：
   ```bash
   # 在您的本地机器上执行
   scp -r user@server-ip:/root/Desktop/MCP/Unity-MCP/Unity-MCP/Editor/ ./Unity-MCP-Plugin/
   ```

2. **复制到 Unity 项目**：
   ```bash
   # 复制到您的 Unity 项目
   cp -r ./Unity-MCP-Plugin/ /path/to/your/unity/project/Assets/Editor/MCP/
   ```

### 方法二：手动下载

1. **从 GitHub 下载**：
   - 访问：https://github.com/CoderGamester/mcp-unity
   - 下载 ZIP 文件并解压

2. **复制 Editor 文件夹**：
   - 将 `Editor` 文件夹复制到您的 Unity 项目的 `Assets/Editor/` 目录

## ⚙️ 配置 Unity MCP 插件

### 1. 打开 MCP 设置窗口

1. 在 Unity Editor 中，打开菜单：`Window > MCP Server`
2. 如果没有看到此菜单，请检查插件是否正确安装

### 2. 配置连接设置

在 MCP Server 窗口中配置以下设置：

```json
{
  "websocketUrl": "ws://YOUR_SERVER_IP:8090",
  "autoReconnect": true,
  "reconnectDelay": 2000,
  "maxReconnectAttempts": 5,
  "requestTimeout": 10000
}
```

**替换 `YOUR_SERVER_IP`** 为您的服务器 IP 地址。

### 3. 启动连接

1. 确保远程服务器已启动：
   ```bash
   # 在服务器上执行
   ./start-unity-remote.sh
   ```

2. 在 Unity Editor 的 MCP Server 窗口中：
   - 点击 "Connect" 或 "Start Server" 按钮
   - 查看连接状态

## 🔍 验证连接

### 1. 检查连接状态

在 Unity Editor 中：
- MCP Server 窗口应显示 "Connected" 状态
- Console 窗口应显示连接成功的日志

### 2. 测试功能

尝试以下操作：
- 在 MCP 客户端中获取场景层级：`unity://scenes-hierarchy`
- 创建游戏对象：使用 `execute_menu_item` 工具
- 查看 Unity 日志：`unity://logs`

## 🚨 故障排除

### 常见问题

#### 1. 连接失败

**症状**：Unity Editor 无法连接到远程服务器

**解决方案**：
```bash
# 检查服务器端口
telnet YOUR_SERVER_IP 8090

# 检查防火墙
ping YOUR_SERVER_IP

# 查看服务器日志
./manage-unity-mcp.sh logs
```

#### 2. 插件未加载

**症状**：Unity Editor 中没有 MCP 菜单

**解决方案**：
1. 检查 `Assets/Editor/` 目录中是否有 MCP 插件文件
2. 重新导入插件：`Assets > Reimport All`
3. 重启 Unity Editor

#### 3. 权限问题

**症状**：WebSocket 连接被拒绝

**解决方案**：
```bash
# 在服务器上检查防火墙
sudo ufw status
sudo ufw allow 8090

# 检查端口占用
lsof -i :8090
```

### 调试步骤

1. **检查网络连通性**：
   ```bash
   ping YOUR_SERVER_IP
   telnet YOUR_SERVER_IP 8090
   ```

2. **查看 Unity Console**：
   - 打开 `Window > General > Console`
   - 查看连接相关的日志信息

3. **检查服务器状态**：
   ```bash
   # 在服务器上执行
   ./manage-unity-mcp.sh status
   curl http://localhost:8940/health
   ```

## 📱 网络配置

### 内网连接

如果 Unity Editor 和服务器在同一内网：
```
WebSocket URL: ws://*************:8090
```

### 外网连接

如果需要通过外网连接：
```
WebSocket URL: ws://YOUR_PUBLIC_IP:8090
```

**注意**：确保路由器已配置端口转发。

### VPN 连接

如果使用 VPN：
```
WebSocket URL: ws://VPN_SERVER_IP:8090
```

## 🎉 成功标志

配置成功后，您应该看到：

1. ✅ Unity Editor 中 MCP Server 窗口显示 "Connected"
2. ✅ Console 中有成功连接的日志
3. ✅ 可以在 MCP 客户端中使用 Unity 工具
4. ✅ 能够获取 Unity 场景和对象信息

## 📚 使用示例

连接成功后，您可以在 MCP 客户端中：

```
@assistant 使用 Unity MCP 帮我：
1. 获取当前场景的层级结构
2. 创建一个空的游戏对象
3. 为该对象添加 Rigidbody 组件
4. 查看 Unity 控制台日志
```

## 🔗 相关链接

- **服务器配置**：参考服务器上的配置文件
- **网络测试**：使用 `test-remote-connection.sh`
- **故障排除**：查看服务器日志和状态

现在您可以在本地 Unity Editor 中享受远程 MCP 服务的强大功能了！🚀
