#!/bin/bash

# Unity MCP 远程服务器启动脚本

echo "🌐 启动 Unity MCP 远程服务器..."

# 获取服务器 IP 地址
SERVER_IP=$(hostname -I | awk '{print $1}')
if [ -z "$SERVER_IP" ]; then
    SERVER_IP=$(ip route get 1 | awk '{print $7; exit}')
fi

echo "📋 服务器信息:"
echo "   - 服务器 IP: $SERVER_IP"
echo "   - Unity WebSocket 端口: 8090"
echo "   - MCP 服务器端口: 8940"

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "⚠️  端口 $port 已被占用，正在尝试终止现有进程..."
        lsof -ti:$port | xargs kill -9 2>/dev/null
        sleep 2
    fi
}

check_port 8090
check_port 8940

# 检查防火墙状态
echo "🔥 检查防火墙配置..."
if command -v ufw >/dev/null 2>&1; then
    if ufw status | grep -q "Status: active"; then
        echo "   防火墙已启用，正在开放必要端口..."
        ufw allow 8090/tcp
        ufw allow 8940/tcp
        echo "   ✅ 端口 8090, 8940 已开放"
    else
        echo "   ✅ 防火墙未启用"
    fi
else
    echo "   ℹ️  未检测到 ufw 防火墙"
fi

# 进入服务器目录
cd Unity-MCP/Server~

# 检查依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

if [ ! -d "build" ]; then
    echo "🔨 构建项目..."
    npm run build
fi

# 创建日志目录
mkdir -p ../../logs

# 设置环境变量
export UNITY_PORT=8090
export UNITY_HOST=0.0.0.0
export UNITY_REQUEST_TIMEOUT=10
export LOGGING=true
export LOGGING_FILE=true
export MCP_SERVER_PORT=8940
export MCP_SERVER_HOST=0.0.0.0
export ALLOW_REMOTE_CONNECTIONS=true

echo ""
echo "🚀 启动远程 Unity MCP 服务器..."
echo ""
echo "📱 本地 Unity Editor 连接信息:"
echo "   WebSocket URL: ws://$SERVER_IP:8090"
echo ""
echo "🔗 MCP 客户端连接信息:"
echo "   SSE 端点: http://$SERVER_IP:8940/sse"
echo "   健康检查: http://$SERVER_IP:8940/health"
echo ""
echo "💡 配置说明:"
echo "   1. 在本地 Unity Editor 中安装 MCP 插件"
echo "   2. 配置 WebSocket 连接到: ws://$SERVER_IP:8090"
echo "   3. 在 MCP 客户端中配置: http://$SERVER_IP:8940/sse"
echo ""

# 创建远程服务器启动脚本
cat > remote-server.js << EOF
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';

const app = express();
const mcpPort = process.env.MCP_SERVER_PORT || 8940;
const unityPort = process.env.UNITY_PORT || 8090;
const host = process.env.MCP_SERVER_HOST || '0.0.0.0';

app.use(cors({
  origin: '*',
  credentials: true
}));
app.use(express.json());

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    service: 'Unity MCP Remote Server',
    unityWebSocket: \`ws://\${host}:\${unityPort}\`,
    mcpEndpoint: \`http://\${host}:\${mcpPort}/sse\`
  });
});

// 服务器信息端点
app.get('/info', (req, res) => {
  res.json({
    serverIP: '$SERVER_IP',
    unityWebSocketPort: unityPort,
    mcpServerPort: mcpPort,
    unityWebSocketURL: \`ws://$SERVER_IP:\${unityPort}\`,
    mcpSSEEndpoint: \`http://$SERVER_IP:\${mcpPort}/sse\`,
    instructions: {
      unityEditor: \`在 Unity Editor 中连接到: ws://$SERVER_IP:\${unityPort}\`,
      mcpClient: \`在 MCP 客户端中配置: http://$SERVER_IP:\${mcpPort}/sse\`
    }
  });
});

// SSE 端点
app.get('/sse', async (req, res) => {
  console.log('New SSE connection from:', req.ip);
  
  const transport = new SSEServerTransport('/messages', res);
  
  try {
    // 动态导入主服务器模块
    const { default: createUnityMcpServer } = await import('./build/index.js');
    
    const server = createUnityMcpServer();
    await server.connect(transport);
    console.log('Unity MCP Server connected via SSE');
  } catch (error) {
    console.error('Failed to connect Unity MCP Server:', error);
    res.status(500).json({ error: 'Failed to start MCP server' });
  }
});

const server = createServer(app);

// 启动 Unity WebSocket 服务器（如果需要）
const wss = new WebSocketServer({
  port: unityPort,
  host: host
});

wss.on('connection', (ws, req) => {
  console.log(\`Unity WebSocket connection from: \${req.socket.remoteAddress}\`);
  
  ws.on('message', (message) => {
    console.log('Received from Unity:', message.toString());
  });
  
  ws.on('close', () => {
    console.log('Unity WebSocket connection closed');
  });
});

server.listen(mcpPort, host, () => {
  console.log(\`🌐 Unity MCP 远程服务器运行在 http://\${host}:\${mcpPort}\`);
  console.log(\`📡 SSE 端点: http://\${host}:\${mcpPort}/sse\`);
  console.log(\`🏥 健康检查: http://\${host}:\${mcpPort}/health\`);
  console.log(\`ℹ️  服务器信息: http://\${host}:\${mcpPort}/info\`);
  console.log(\`🎮 Unity WebSocket: ws://\${host}:\${unityPort}\`);
});

console.log(\`🎮 Unity WebSocket 服务器运行在 ws://\${host}:\${unityPort}\`);

process.on('SIGINT', () => {
  console.log('\\n🛑 正在关闭服务器...');
  wss.close();
  server.close(() => {
    process.exit(0);
  });
});
EOF

# 启动远程服务器
node remote-server.js

echo "🛑 Unity MCP 远程服务器已停止"
