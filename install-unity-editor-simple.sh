#!/bin/bash

# 简化的 Unity Editor 安装脚本

echo "🎮 简化 Unity Editor 安装..."

# 1. 下载 Unity Editor (较小的版本)
download_unity() {
    echo "📥 下载 Unity Editor..."
    
    cd /opt/unity
    
    # 尝试下载 Unity Editor 的 tar.xz 文件
    echo "正在下载 Unity 2022.3.45f1..."
    
    # 使用 curl 替代 wget，可能更稳定
    curl -L -o Unity-2022.3.45f1.tar.xz \
        "https://download.unity3d.com/download_unity/c2d5a7410213/LinuxEditorInstaller/Unity-2022.3.45f1.tar.xz" \
        --connect-timeout 30 \
        --max-time 1800 \
        --retry 3 \
        --retry-delay 5
    
    if [ $? -eq 0 ] && [ -s Unity-2022.3.45f1.tar.xz ]; then
        echo "✅ Unity Editor 下载完成"
        return 0
    else
        echo "❌ Unity Editor 下载失败"
        return 1
    fi
}

# 2. 解压和安装 Unity Editor
install_unity() {
    echo "📦 解压 Unity Editor..."
    
    cd /opt/unity
    
    if [ -f Unity-2022.3.45f1.tar.xz ] && [ -s Unity-2022.3.45f1.tar.xz ]; then
        # 解压 Unity Editor
        tar -xf Unity-2022.3.45f1.tar.xz
        
        if [ $? -eq 0 ]; then
            echo "✅ Unity Editor 解压完成"
            
            # 查找 Unity 可执行文件
            UNITY_EXEC=$(find /opt/unity -name "Unity" -type f -executable | head -1)
            
            if [ -n "$UNITY_EXEC" ]; then
                echo "✅ Unity Editor 可执行文件: $UNITY_EXEC"
                
                # 创建符号链接
                ln -sf "$UNITY_EXEC" /opt/unity/Unity
                
                # 更新启动脚本
                cat > /opt/unity/start-unity-editor-direct.sh << EOF
#!/bin/bash
export DISPLAY=:1
cd /opt/unity
echo "🎮 启动 Unity Editor..."
echo "📁 项目路径: /root/Desktop/MCP/Unity-Test-Project"
"$UNITY_EXEC" -projectPath "/root/Desktop/MCP/Unity-Test-Project" -logFile /tmp/unity.log
EOF
                chmod +x /opt/unity/start-unity-editor-direct.sh
                
                echo "✅ Unity Editor 安装完成"
                return 0
            else
                echo "❌ 未找到 Unity 可执行文件"
                return 1
            fi
        else
            echo "❌ Unity Editor 解压失败"
            return 1
        fi
    else
        echo "❌ Unity Editor 文件不存在或为空"
        return 1
    fi
}

# 3. 创建测试脚本
create_test() {
    echo "🧪 创建 Unity 测试脚本..."
    
    cat > /root/Desktop/MCP/test-unity-editor.sh << 'EOF'
#!/bin/bash

echo "🧪 测试 Unity Editor 安装..."

# 检查 Unity Editor
if [ -f "/opt/unity/Unity" ]; then
    echo "✅ Unity Editor 已安装"
    
    # 检查版本
    export DISPLAY=:1
    /opt/unity/Unity -version 2>/dev/null | head -5
    
    echo ""
    echo "🚀 启动 Unity Editor:"
    echo "   方法1: /opt/unity/start-unity-editor-direct.sh"
    echo "   方法2: /opt/unity/start-unity-hub-vnc.sh"
    echo ""
    echo "📁 项目路径: /root/Desktop/MCP/Unity-Test-Project"
    
else
    echo "❌ Unity Editor 未安装"
    echo "💡 运行安装脚本: ./install-unity-editor-simple.sh"
fi

# 检查 VNC
if pgrep -f "Xvnc.*:1" >/dev/null; then
    echo "✅ VNC 服务器运行中"
    echo "🔗 连接地址: $(hostname -I | awk '{print $1}'):5901"
else
    echo "❌ VNC 服务器未运行"
    echo "🚀 启动 VNC: vncserver :1 -geometry 1920x1080 -depth 24"
fi
EOF
    chmod +x /root/Desktop/MCP/test-unity-editor.sh
    
    echo "✅ 测试脚本创建完成"
}

# 主程序
main() {
    echo "🚀 开始简化安装..."
    
    create_test
    
    echo ""
    echo "📋 安装选项:"
    echo "1. 自动下载安装: 运行此脚本的下载功能"
    echo "2. 手动安装: 通过 VNC 使用 Unity Hub"
    echo ""
    
    read -p "是否现在下载 Unity Editor? (y/n): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if download_unity; then
            install_unity
        fi
    fi
    
    echo ""
    echo "🎯 下一步:"
    echo "1. 测试安装: ./test-unity-editor.sh"
    echo "2. 连接 VNC: $(hostname -I | awk '{print $1}'):5901"
    echo "3. 启动 Unity: /opt/unity/start-unity-hub-vnc.sh"
    echo ""
}

# 运行主程序
main "$@"
