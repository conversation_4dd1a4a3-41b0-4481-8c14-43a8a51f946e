#!/bin/bash

# Unity MCP 本地模式启动脚本（适用于远程开发）

echo "🎮 启动 Unity MCP 本地模式（远程开发环境）..."

# 检查当前环境
check_environment() {
    echo "🔍 检查环境..."
    
    # 检查是否在远程环境
    if [ -n "$SSH_CLIENT" ] || [ -n "$SSH_TTY" ]; then
        echo "✅ 检测到 SSH 远程环境"
        REMOTE_ENV=true
    else
        echo "ℹ️  本地环境"
        REMOTE_ENV=false
    fi
    
    # 检查图形界面
    if [ -n "$DISPLAY" ]; then
        echo "✅ 图形界面可用: $DISPLAY"
    else
        echo "⚠️  未检测到图形界面，Unity Editor 可能无法启动"
    fi
    
    # 检查 Unity 进程
    if pgrep -f "Unity" >/dev/null; then
        echo "✅ Unity Editor 正在运行"
    else
        echo "⚠️  Unity Editor 未运行"
    fi
}

# 配置本地模式
setup_local_mode() {
    echo "⚙️  配置本地模式..."
    
    # 停止远程模式服务器（如果在运行）
    if lsof -Pi :8940 -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "🛑 停止现有的远程模式服务器..."
        lsof -ti:8940 | xargs kill -9 2>/dev/null
        sleep 2
    fi
    
    # 检查端口
    if lsof -Pi :8090 -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "⚠️  端口 8090 已被占用"
        lsof -i :8090
    fi
    
    if lsof -Pi :8940 -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "⚠️  端口 8940 已被占用"
        lsof -i :8940
    fi
}

# 启动本地模式服务器
start_local_server() {
    echo "🚀 启动本地模式 Unity MCP 服务器..."
    
    # 进入服务器目录
    cd Unity-MCP/Server~
    
    # 检查构建
    if [ ! -d "build" ]; then
        echo "🔨 构建项目..."
        npm run build
    fi
    
    # 创建日志目录
    mkdir -p ../../logs
    
    # 设置环境变量（本地模式）
    export UNITY_PORT=8090
    export UNITY_HOST=localhost
    export UNITY_REQUEST_TIMEOUT=10
    export LOGGING=true
    export LOGGING_FILE=true
    export MCP_SERVER_PORT=8940
    export MCP_SERVER_HOST=localhost
    export ALLOW_REMOTE_CONNECTIONS=false
    
    echo ""
    echo "📋 本地模式配置:"
    echo "   - Unity WebSocket: ws://localhost:8090"
    echo "   - MCP 服务器: http://localhost:8940/sse"
    echo "   - 健康检查: http://localhost:8940/health"
    echo "   - 日志目录: ../../logs"
    echo ""
    
    if [ "$REMOTE_ENV" = true ]; then
        echo "🌐 远程开发环境提示:"
        echo "   - 在远程桌面内访问: http://localhost:8940/sse"
        echo "   - 外部访问需要端口转发或使用远程模式"
        echo ""
    fi
    
    echo "💡 使用说明:"
    echo "   1. 确保 Unity Editor 已启动并安装了 MCP 插件"
    echo "   2. 在 Unity Editor 中连接到: ws://localhost:8090"
    echo "   3. 在 MCP 客户端中配置: http://localhost:8940/sse"
    echo ""
    
    # 创建本地模式服务器脚本
    cat > local-server.js << 'EOF'
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import express from 'express';
import cors from 'cors';
import { createServer } from 'http';

const app = express();
const mcpPort = process.env.MCP_SERVER_PORT || 8940;
const unityPort = process.env.UNITY_PORT || 8090;
const host = 'localhost';

app.use(cors({
  origin: ['http://localhost:*', 'http://127.0.0.1:*'],
  credentials: true
}));
app.use(express.json());

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    service: 'Unity MCP Local Server',
    mode: 'local',
    unityWebSocket: `ws://${host}:${unityPort}`,
    mcpEndpoint: `http://${host}:${mcpPort}/sse`
  });
});

// 服务器信息端点
app.get('/info', (req, res) => {
  res.json({
    mode: 'local',
    unityWebSocketPort: unityPort,
    mcpServerPort: mcpPort,
    unityWebSocketURL: `ws://${host}:${unityPort}`,
    mcpSSEEndpoint: `http://${host}:${mcpPort}/sse`,
    instructions: {
      unityEditor: `在 Unity Editor 中连接到: ws://${host}:${unityPort}`,
      mcpClient: `在 MCP 客户端中配置: http://${host}:${mcpPort}/sse`
    }
  });
});

// SSE 端点
app.get('/sse', async (req, res) => {
  console.log('New SSE connection from:', req.ip);
  
  const transport = new SSEServerTransport('/messages', res);
  
  try {
    // 动态导入主服务器模块
    const { default: createUnityMcpServer } = await import('./build/index.js');
    
    const server = createUnityMcpServer();
    await server.connect(transport);
    console.log('Unity MCP Server connected via SSE (Local Mode)');
  } catch (error) {
    console.error('Failed to connect Unity MCP Server:', error);
    res.status(500).json({ error: 'Failed to start MCP server' });
  }
});

const server = createServer(app);

server.listen(mcpPort, host, () => {
  console.log(`🎮 Unity MCP 本地服务器运行在 http://${host}:${mcpPort}`);
  console.log(`📡 SSE 端点: http://${host}:${mcpPort}/sse`);
  console.log(`🏥 健康检查: http://${host}:${mcpPort}/health`);
  console.log(`ℹ️  服务器信息: http://${host}:${mcpPort}/info`);
  console.log(`🎮 Unity WebSocket 期望: ws://${host}:${unityPort}`);
});

process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭本地服务器...');
  server.close(() => {
    process.exit(0);
  });
});
EOF

    # 启动本地服务器
    node local-server.js
}

# 主程序
main() {
    check_environment
    setup_local_mode
    start_local_server
}

# 运行主程序
main "$@"

echo "🛑 Unity MCP 本地服务器已停止"
