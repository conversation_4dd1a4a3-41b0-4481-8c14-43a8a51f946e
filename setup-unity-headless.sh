#!/bin/bash

# Unity Headless Mode 安装和配置脚本

echo "🎮 配置 Unity Headless Mode..."

# 1. 下载 Unity Editor (Headless)
download_unity_headless() {
    echo "📥 下载 Unity Editor (Headless 版本)..."
    
    cd /opt/unity
    mkdir -p headless
    cd headless
    
    # Unity 2022.3.45f1 Linux Editor
    echo "正在下载 Unity 2022.3.45f1 Linux Editor..."
    
    # 使用更稳定的下载方式
    wget -c -t 3 -T 30 \
        "https://download.unity3d.com/download_unity/c2d5a7410213/LinuxEditorInstaller/Unity-2022.3.45f1.tar.xz" \
        -O Unity-2022.3.45f1.tar.xz
    
    if [ $? -eq 0 ] && [ -s Unity-2022.3.45f1.tar.xz ]; then
        echo "✅ Unity Editor 下载完成"
        
        # 解压
        echo "📦 解压 Unity Editor..."
        tar -xf Unity-2022.3.45f1.tar.xz
        
        if [ $? -eq 0 ]; then
            echo "✅ Unity Editor 解压完成"
            
            # 查找 Unity 可执行文件
            UNITY_EXEC=$(find /opt/unity/headless -name "Unity" -type f -executable | head -1)
            
            if [ -n "$UNITY_EXEC" ]; then
                echo "✅ Unity Editor 可执行文件: $UNITY_EXEC"
                
                # 创建符号链接
                ln -sf "$UNITY_EXEC" /opt/unity/Unity-Headless
                
                return 0
            else
                echo "❌ 未找到 Unity 可执行文件"
                return 1
            fi
        else
            echo "❌ Unity Editor 解压失败"
            return 1
        fi
    else
        echo "❌ Unity Editor 下载失败"
        return 1
    fi
}

# 2. 创建 Unity Headless 启动脚本
create_headless_scripts() {
    echo "📝 创建 Unity Headless 启动脚本..."
    
    # 基本的 Headless 启动脚本
    cat > /opt/unity/start-unity-headless.sh << 'EOF'
#!/bin/bash

# Unity Headless Mode 启动脚本

echo "🎮 启动 Unity Headless Mode..."

# 设置环境变量
export DISPLAY=:99  # 虚拟显示
export UNITY_PROJECT_PATH="/root/Desktop/MCP/Unity-Test-Project"

# 检查项目路径
if [ ! -d "$UNITY_PROJECT_PATH" ]; then
    echo "❌ Unity 项目不存在: $UNITY_PROJECT_PATH"
    exit 1
fi

# 查找 Unity 可执行文件
UNITY_EXEC=""
if [ -f "/opt/unity/Unity-Headless" ]; then
    UNITY_EXEC="/opt/unity/Unity-Headless"
elif [ -f "/opt/unity/Unity" ]; then
    UNITY_EXEC="/opt/unity/Unity"
else
    echo "❌ 未找到 Unity 可执行文件"
    exit 1
fi

echo "🎯 Unity 可执行文件: $UNITY_EXEC"
echo "📁 项目路径: $UNITY_PROJECT_PATH"

# 启动虚拟显示（如果需要）
if ! pgrep -f "Xvfb.*:99" >/dev/null; then
    echo "🖥️ 启动虚拟显示..."
    Xvfb :99 -screen 0 1024x768x24 &
    XVFB_PID=$!
    sleep 2
fi

# 启动 Unity Headless
echo "🚀 启动 Unity Headless..."
"$UNITY_EXEC" \
    -batchmode \
    -nographics \
    -silent-crashes \
    -logFile /tmp/unity-headless.log \
    -projectPath "$UNITY_PROJECT_PATH" \
    -quit &

UNITY_PID=$!

echo "✅ Unity Headless 已启动 (PID: $UNITY_PID)"
echo "📋 日志文件: /tmp/unity-headless.log"
echo "🔍 检查进程: ps aux | grep Unity"

# 等待 Unity 启动
sleep 5

# 检查 Unity 是否成功启动
if ps -p $UNITY_PID > /dev/null; then
    echo "✅ Unity Headless 运行正常"
else
    echo "❌ Unity Headless 启动失败"
    echo "📋 查看日志: tail -f /tmp/unity-headless.log"
fi

# 清理函数
cleanup() {
    echo "🛑 停止 Unity Headless..."
    kill $UNITY_PID 2>/dev/null
    if [ -n "$XVFB_PID" ]; then
        kill $XVFB_PID 2>/dev/null
    fi
}

trap cleanup EXIT

# 保持脚本运行
wait $UNITY_PID
EOF

    chmod +x /opt/unity/start-unity-headless.sh
    
    # 创建后台运行脚本
    cat > /opt/unity/start-unity-background.sh << 'EOF'
#!/bin/bash

# Unity 后台运行脚本

echo "🎮 启动 Unity 后台模式..."

# 设置环境变量
export UNITY_PROJECT_PATH="/root/Desktop/MCP/Unity-Test-Project"

# 查找 Unity 可执行文件
UNITY_EXEC=""
if [ -f "/opt/unity/Unity-Headless" ]; then
    UNITY_EXEC="/opt/unity/Unity-Headless"
elif [ -f "/opt/unity/Unity" ]; then
    UNITY_EXEC="/opt/unity/Unity"
else
    echo "❌ 未找到 Unity 可执行文件"
    exit 1
fi

# 启动虚拟显示
if ! pgrep -f "Xvfb.*:99" >/dev/null; then
    echo "🖥️ 启动虚拟显示..."
    Xvfb :99 -screen 0 1024x768x24 >/dev/null 2>&1 &
    sleep 2
fi

# 启动 Unity 后台模式
echo "🚀 启动 Unity 后台模式..."
export DISPLAY=:99

nohup "$UNITY_EXEC" \
    -batchmode \
    -nographics \
    -projectPath "$UNITY_PROJECT_PATH" \
    -logFile /tmp/unity-background.log \
    > /tmp/unity-background-output.log 2>&1 &

UNITY_PID=$!
echo $UNITY_PID > /tmp/unity-background.pid

echo "✅ Unity 后台模式已启动"
echo "📋 PID: $UNITY_PID"
echo "📋 PID 文件: /tmp/unity-background.pid"
echo "📋 日志文件: /tmp/unity-background.log"
echo "📋 输出日志: /tmp/unity-background-output.log"
echo ""
echo "🔍 管理命令:"
echo "   查看进程: ps aux | grep Unity"
echo "   查看日志: tail -f /tmp/unity-background.log"
echo "   停止服务: kill \$(cat /tmp/unity-background.pid)"
EOF

    chmod +x /opt/unity/start-unity-background.sh
    
    # 创建 Unity 管理脚本
    cat > /opt/unity/manage-unity-headless.sh << 'EOF'
#!/bin/bash

# Unity Headless 管理脚本

case $1 in
    "start")
        echo "🚀 启动 Unity 后台模式..."
        /opt/unity/start-unity-background.sh
        ;;
    "stop")
        echo "🛑 停止 Unity 后台模式..."
        if [ -f /tmp/unity-background.pid ]; then
            PID=$(cat /tmp/unity-background.pid)
            if ps -p $PID > /dev/null; then
                kill $PID
                echo "✅ Unity 进程已停止 (PID: $PID)"
            else
                echo "❌ Unity 进程不存在"
            fi
            rm -f /tmp/unity-background.pid
        else
            echo "❌ PID 文件不存在"
        fi
        
        # 停止虚拟显示
        pkill -f "Xvfb.*:99" 2>/dev/null
        ;;
    "status")
        echo "🔍 Unity 后台模式状态:"
        if [ -f /tmp/unity-background.pid ]; then
            PID=$(cat /tmp/unity-background.pid)
            if ps -p $PID > /dev/null; then
                echo "   ✅ Unity 运行中 (PID: $PID)"
                echo "   📊 内存使用: $(ps -p $PID -o rss= | awk '{print $1/1024 " MB"}')"
                echo "   ⏱️  运行时间: $(ps -p $PID -o etime= | tr -d ' ')"
            else
                echo "   ❌ Unity 未运行"
            fi
        else
            echo "   ❌ Unity 未启动"
        fi
        
        # 检查虚拟显示
        if pgrep -f "Xvfb.*:99" >/dev/null; then
            echo "   ✅ 虚拟显示运行中"
        else
            echo "   ❌ 虚拟显示未运行"
        fi
        
        # 检查端口
        if lsof -i :8090 >/dev/null 2>&1; then
            echo "   ✅ Unity WebSocket 端口 8090 已开放"
        else
            echo "   ❌ Unity WebSocket 端口 8090 未开放"
        fi
        ;;
    "logs")
        echo "📋 Unity 日志:"
        if [ -f /tmp/unity-background.log ]; then
            tail -f /tmp/unity-background.log
        else
            echo "❌ 日志文件不存在"
        fi
        ;;
    "restart")
        echo "🔄 重启 Unity 后台模式..."
        $0 stop
        sleep 3
        $0 start
        ;;
    *)
        echo "Unity Headless 管理脚本"
        echo "用法: $0 {start|stop|status|logs|restart}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动 Unity 后台模式"
        echo "  stop    - 停止 Unity 后台模式"
        echo "  status  - 查看运行状态"
        echo "  logs    - 查看实时日志"
        echo "  restart - 重启服务"
        ;;
esac
EOF

    chmod +x /opt/unity/manage-unity-headless.sh
    
    echo "✅ Unity Headless 脚本创建完成"
}

# 3. 安装虚拟显示依赖
install_virtual_display() {
    echo "📦 安装虚拟显示依赖..."
    
    apt update
    apt install -y xvfb
    
    if [ $? -eq 0 ]; then
        echo "✅ 虚拟显示依赖安装完成"
    else
        echo "❌ 虚拟显示依赖安装失败"
        return 1
    fi
}

# 4. 创建测试脚本
create_test_scripts() {
    echo "🧪 创建 Unity Headless 测试脚本..."
    
    cat > /root/Desktop/MCP/test-unity-headless.sh << 'EOF'
#!/bin/bash

echo "🧪 测试 Unity Headless Mode..."

# 1. 检查 Unity 安装
echo "1. 检查 Unity 安装..."
if [ -f "/opt/unity/Unity-Headless" ] || [ -f "/opt/unity/Unity" ]; then
    echo "   ✅ Unity 可执行文件存在"
else
    echo "   ❌ Unity 可执行文件不存在"
    echo "   💡 运行安装: ./setup-unity-headless.sh"
    exit 1
fi

# 2. 检查项目
echo "2. 检查 Unity 项目..."
if [ -d "/root/Desktop/MCP/Unity-Test-Project" ]; then
    echo "   ✅ Unity 项目存在"
else
    echo "   ❌ Unity 项目不存在"
    exit 1
fi

# 3. 启动 Unity Headless
echo "3. 启动 Unity Headless..."
/opt/unity/manage-unity-headless.sh start

# 4. 等待启动
echo "4. 等待 Unity 启动..."
sleep 10

# 5. 检查状态
echo "5. 检查 Unity 状态..."
/opt/unity/manage-unity-headless.sh status

# 6. 启动 Unity MCP 模拟器
echo "6. 启动 Unity MCP 模拟器..."
if ! lsof -i :8090 >/dev/null 2>&1; then
    echo "   启动 Unity WebSocket 模拟器..."
    node unity-simulator.js &
    SIMULATOR_PID=$!
    sleep 3
fi

# 7. 测试 Unity MCP 连接
echo "7. 测试 Unity MCP 连接..."
if lsof -i :8090 >/dev/null 2>&1; then
    echo "   ✅ Unity WebSocket 端口 8090 已开放"
    
    echo "8. 测试 Cursor Unity MCP 功能..."
    echo "   现在可以在 Cursor 中测试:"
    echo "   @cursor 使用 Unity MCP 获取场景信息"
    echo "   @cursor 使用 Unity MCP 创建游戏对象"
    echo "   @cursor 使用 Unity MCP 查看日志"
else
    echo "   ❌ Unity WebSocket 端口 8090 未开放"
fi

echo ""
echo "🎯 管理命令:"
echo "   /opt/unity/manage-unity-headless.sh status   # 查看状态"
echo "   /opt/unity/manage-unity-headless.sh logs     # 查看日志"
echo "   /opt/unity/manage-unity-headless.sh stop     # 停止服务"
EOF

    chmod +x /root/Desktop/MCP/test-unity-headless.sh
    
    echo "✅ 测试脚本创建完成"
}

# 主程序
main() {
    echo "🚀 开始配置 Unity Headless Mode..."
    
    install_virtual_display
    create_headless_scripts
    create_test_scripts
    
    echo ""
    echo "📋 安装选项:"
    echo "1. 下载并安装 Unity Editor"
    echo "2. 仅创建脚本（稍后手动安装）"
    echo ""
    
    read -p "是否现在下载 Unity Editor? (y/n): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        download_unity_headless
    fi
    
    echo ""
    echo "🎉 Unity Headless Mode 配置完成！"
    echo ""
    echo "🚀 使用方法:"
    echo "1. 启动 Unity 后台: /opt/unity/manage-unity-headless.sh start"
    echo "2. 查看状态: /opt/unity/manage-unity-headless.sh status"
    echo "3. 测试功能: ./test-unity-headless.sh"
    echo ""
    echo "💡 优势:"
    echo "   - 无需图形界面"
    echo "   - 资源占用更少"
    echo "   - 适合服务器环境"
    echo "   - 支持完整的 Unity MCP 功能"
}

# 运行主程序
main "$@"
