import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';

const app = express();
const mcpPort = process.env.MCP_SERVER_PORT || 8940;
const unityPort = process.env.UNITY_PORT || 8090;
const host = process.env.MCP_SERVER_HOST || '0.0.0.0';

app.use(cors({
  origin: '*',
  credentials: true
}));
app.use(express.json());

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    service: 'Unity MCP Remote Server',
    unityWebSocket: `ws://${host}:${unityPort}`,
    mcpEndpoint: `http://${host}:${mcpPort}/sse`
  });
});

// 服务器信息端点
app.get('/info', (req, res) => {
  res.json({
    serverIP: '***************',
    unityWebSocketPort: unityPort,
    mcpServerPort: mcpPort,
    unityWebSocketURL: `ws://***************:${unityPort}`,
    mcpSSEEndpoint: `http://***************:${mcpPort}/sse`,
    instructions: {
      unityEditor: `在 Unity Editor 中连接到: ws://***************:${unityPort}`,
      mcpClient: `在 MCP 客户端中配置: http://***************:${mcpPort}/sse`
    }
  });
});

// SSE 端点
app.get('/sse', async (req, res) => {
  console.log('New SSE connection from:', req.ip);
  
  const transport = new SSEServerTransport('/messages', res);
  
  try {
    // 动态导入主服务器模块
    const { default: createUnityMcpServer } = await import('./build/index.js');
    
    const server = createUnityMcpServer();
    await server.connect(transport);
    console.log('Unity MCP Server connected via SSE');
  } catch (error) {
    console.error('Failed to connect Unity MCP Server:', error);
    res.status(500).json({ error: 'Failed to start MCP server' });
  }
});

const server = createServer(app);

// 启动 Unity WebSocket 服务器（如果需要）
const wss = new WebSocketServer({
  port: unityPort,
  host: host
});

wss.on('connection', (ws, req) => {
  console.log(`Unity WebSocket connection from: ${req.socket.remoteAddress}`);
  
  ws.on('message', (message) => {
    console.log('Received from Unity:', message.toString());
  });
  
  ws.on('close', () => {
    console.log('Unity WebSocket connection closed');
  });
});

server.listen(mcpPort, host, () => {
  console.log(`🌐 Unity MCP 远程服务器运行在 http://${host}:${mcpPort}`);
  console.log(`📡 SSE 端点: http://${host}:${mcpPort}/sse`);
  console.log(`🏥 健康检查: http://${host}:${mcpPort}/health`);
  console.log(`ℹ️  服务器信息: http://${host}:${mcpPort}/info`);
  console.log(`🎮 Unity WebSocket: ws://${host}:${unityPort}`);
});

console.log(`🎮 Unity WebSocket 服务器运行在 ws://${host}:${unityPort}`);

process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  wss.close();
  server.close(() => {
    process.exit(0);
  });
});
