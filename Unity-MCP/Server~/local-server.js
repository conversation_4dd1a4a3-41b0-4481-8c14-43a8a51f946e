import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import express from 'express';
import cors from 'cors';
import { createServer } from 'http';

const app = express();
const mcpPort = process.env.MCP_SERVER_PORT || 8940;
const unityPort = process.env.UNITY_PORT || 8090;
const host = 'localhost';

app.use(cors({
  origin: ['http://localhost:*', 'http://127.0.0.1:*'],
  credentials: true
}));
app.use(express.json());

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    service: 'Unity MCP Local Server',
    mode: 'local',
    unityWebSocket: `ws://${host}:${unityPort}`,
    mcpEndpoint: `http://${host}:${mcpPort}/sse`
  });
});

// 服务器信息端点
app.get('/info', (req, res) => {
  res.json({
    mode: 'local',
    unityWebSocketPort: unityPort,
    mcpServerPort: mcpPort,
    unityWebSocketURL: `ws://${host}:${unityPort}`,
    mcpSSEEndpoint: `http://${host}:${mcpPort}/sse`,
    instructions: {
      unityEditor: `在 Unity Editor 中连接到: ws://${host}:${unityPort}`,
      mcpClient: `在 MCP 客户端中配置: http://${host}:${mcpPort}/sse`
    }
  });
});

// SSE 端点
app.get('/sse', async (req, res) => {
  console.log('New SSE connection from:', req.ip);
  
  const transport = new SSEServerTransport('/messages', res);
  
  try {
    // 动态导入主服务器模块
    const { default: createUnityMcpServer } = await import('./build/index.js');
    
    const server = createUnityMcpServer();
    await server.connect(transport);
    console.log('Unity MCP Server connected via SSE (Local Mode)');
  } catch (error) {
    console.error('Failed to connect Unity MCP Server:', error);
    res.status(500).json({ error: 'Failed to start MCP server' });
  }
});

const server = createServer(app);

server.listen(mcpPort, host, () => {
  console.log(`🎮 Unity MCP 本地服务器运行在 http://${host}:${mcpPort}`);
  console.log(`📡 SSE 端点: http://${host}:${mcpPort}/sse`);
  console.log(`🏥 健康检查: http://${host}:${mcpPort}/health`);
  console.log(`ℹ️  服务器信息: http://${host}:${mcpPort}/info`);
  console.log(`🎮 Unity WebSocket 期望: ws://${host}:${unityPort}`);
});

process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭本地服务器...');
  server.close(() => {
    process.exit(0);
  });
});
