# 🚀 VS Code 中配置 Playwright MCP 服务 - 详细指南

## 📋 准备工作

✅ **已完成**：
- Node.js v22.16.0 (满足要求 ≥18)
- VS Code 1.100.0
- 配置文件已创建

## 🔧 配置步骤

### 步骤 1：选择配置方法

我为您准备了三种配置方法，请选择最适合的：

#### 方法 A：使用工作区文件（推荐）
1. 在 VS Code 中打开 `playwright-mcp.code-workspace` 文件
2. 点击右下角的 "Open Workspace" 按钮
3. 重启 VS Code

#### 方法 B：修改用户设置
1. 在 VS Code 中按 `Ctrl+,`（Windows/Linux）或 `Cmd+,`（macOS）
2. 点击右上角的 "Open Settings (JSON)" 图标
3. 将 `vscode-settings.json` 的内容复制到您的 settings.json 中
4. 保存文件并重启 VS Code

#### 方法 C：手动配置
1. 按 `Ctrl+Shift+P`（Windows/Linux）或 `Cmd+Shift+P`（macOS）
2. 输入 "Preferences: Open Settings (JSON)"
3. 添加以下配置：

```json
{
  "mcp.servers": {
    "playwright": {
      "command": "npx",
      "args": ["@playwright/mcp@latest"]
    }
  }
}
```

### 步骤 2：验证配置

1. **重启 VS Code**
2. **检查 MCP 状态**：
   - 按 `Ctrl+Shift+P` 打开命令面板
   - 搜索 "MCP" 查看相关命令
3. **查看输出日志**：
   - 打开 `View > Output`
   - 在下拉菜单中选择 "MCP" 查看日志

### 步骤 3：安装必要的扩展

确保安装了以下扩展：
- **GitHub Copilot** (ms-vscode.vscode-copilot)
- **GitHub Copilot Chat** (ms-vscode.vscode-copilot-chat)

## 🎯 使用方法

### 与 GitHub Copilot 交互

配置完成后，您可以在 Copilot Chat 中使用 Playwright：

```
@copilot 请使用 Playwright 帮我：
1. 打开 https://www.baidu.com
2. 截取页面截图
3. 搜索"人工智能"
4. 保存结果页面为 PDF
```

### 常用命令示例

```
# 基础操作
@copilot 用 Playwright 打开 GitHub 首页并截图

# 表单操作  
@copilot 用 Playwright 填写登录表单

# 数据抓取
@copilot 用 Playwright 抓取新闻网站的标题

# 测试生成
@copilot 为购物车功能生成 Playwright 测试代码
```

## 🔍 故障排除

### 问题 1：MCP 服务器未启动

**症状**：在命令面板中找不到 MCP 相关命令

**解决方案**：
1. 检查 settings.json 语法是否正确
2. 确认 Node.js 版本 ≥18
3. 重启 VS Code
4. 查看输出日志中的错误信息

### 问题 2：Playwright 包下载失败

**症状**：首次使用时报错

**解决方案**：
```bash
# 手动安装
cd /root/Desktop/MCP
npm install -g @playwright/mcp@latest
npx playwright install chrome
```

### 问题 3：权限问题

**症状**：浏览器启动失败

**解决方案**：
在配置中添加 `--no-sandbox` 参数：
```json
{
  "mcp.servers": {
    "playwright": {
      "command": "npx",
      "args": ["@playwright/mcp@latest", "--no-sandbox"]
    }
  }
}
```

### 问题 4：VS Code 版本不支持 MCP

**症状**：找不到 MCP 相关设置

**解决方案**：
1. 更新 VS Code 到最新版本
2. 或者使用 VS Code Insiders 版本
3. 确认已安装 GitHub Copilot 扩展

## 📊 配置验证清单

- [ ] VS Code 版本 ≥1.90
- [ ] Node.js 版本 ≥18
- [ ] GitHub Copilot 扩展已安装
- [ ] MCP 配置已添加到 settings.json
- [ ] VS Code 已重启
- [ ] 命令面板中可以找到 MCP 命令
- [ ] 输出日志中没有错误信息

## 🎉 成功标志

配置成功后，您应该能够：

1. ✅ 在命令面板中看到 MCP 相关命令
2. ✅ 在 Copilot Chat 中使用 Playwright 工具
3. ✅ 执行浏览器自动化任务
4. ✅ 生成 Playwright 测试代码

## 📞 需要帮助？

如果遇到问题，请：
1. 检查输出日志中的错误信息
2. 确认所有配置步骤都已完成
3. 尝试重启 VS Code
4. 查看本目录中的其他文档文件

祝您使用愉快！🚀
