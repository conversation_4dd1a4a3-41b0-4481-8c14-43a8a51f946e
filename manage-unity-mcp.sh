#!/bin/bash

# Unity MCP 服务器管理脚本

show_help() {
    echo "🎮 Unity MCP 服务器管理工具"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  install          安装依赖和构建项目"
    echo "  start [mode]     启动服务器"
    echo "  stop             停止服务器"
    echo "  status           查看服务器状态"
    echo "  restart [mode]   重启服务器"
    echo "  logs             查看日志"
    echo "  test             测试服务器连接"
    echo ""
    echo "模式 (mode):"
    echo "  stdio            STDIO 模式 (默认)"
    echo "  standalone       独立服务器模式"
    echo ""
    echo "示例:"
    echo "  $0 install           # 安装依赖"
    echo "  $0 start stdio       # 启动 STDIO 模式"
    echo "  $0 start standalone  # 启动独立服务器"
    echo "  $0 status            # 查看状态"
}

check_dependencies() {
    echo "🔍 检查依赖..."
    
    # 检查 Node.js
    if ! command -v node >/dev/null 2>&1; then
        echo "❌ Node.js 未安装"
        return 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    echo "✅ Node.js: v$NODE_VERSION"
    
    # 检查项目目录
    if [ ! -d "Unity-MCP/Server~" ]; then
        echo "❌ Unity MCP 项目目录不存在"
        return 1
    fi
    
    echo "✅ 项目目录存在"
    return 0
}

install_dependencies() {
    echo "📦 安装 Unity MCP 依赖..."
    
    cd Unity-MCP/Server~
    
    if [ ! -f "package.json" ]; then
        echo "❌ package.json 不存在"
        return 1
    fi
    
    npm install
    
    if [ $? -eq 0 ]; then
        echo "✅ 依赖安装完成"
        
        echo "🔨 构建 TypeScript 代码..."
        npm run build
        
        if [ $? -eq 0 ]; then
            echo "✅ 构建完成"
        else
            echo "❌ 构建失败"
            return 1
        fi
    else
        echo "❌ 依赖安装失败"
        return 1
    fi
    
    cd ../..
}

check_port() {
    local port=$1
    if command -v lsof >/dev/null 2>&1; then
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            return 0  # 端口被占用
        else
            return 1  # 端口空闲
        fi
    else
        if netstat -ln 2>/dev/null | grep -q ":$port "; then
            return 0
        else
            return 1
        fi
    fi
}

start_server() {
    local mode=${1:-stdio}
    
    if ! check_dependencies; then
        echo "❌ 依赖检查失败"
        return 1
    fi
    
    case $mode in
        "stdio")
            echo "🚀 启动 Unity MCP 服务器 (STDIO 模式)..."
            ./start-unity-mcp.sh
            ;;
        "standalone")
            echo "🚀 启动 Unity MCP 独立服务器..."
            ./start-unity-standalone.sh &
            ;;
        *)
            echo "❌ 未知模式: $mode"
            echo "可用模式: stdio, standalone"
            return 1
            ;;
    esac
}

stop_server() {
    echo "🛑 停止 Unity MCP 服务器..."
    
    # 停止独立服务器
    if check_port 8940; then
        echo "   停止独立服务器 (端口 8940)..."
        lsof -ti:8940 | xargs kill -9 2>/dev/null
    fi
    
    # 停止其他可能的进程
    pkill -f "unity.*mcp" 2>/dev/null
    pkill -f "mcp.*unity" 2>/dev/null
    
    echo "✅ 服务器已停止"
}

show_status() {
    echo "📊 Unity MCP 服务器状态:"
    echo ""
    
    # 检查独立服务器
    if check_port 8940; then
        local pid=$(lsof -ti:8940 2>/dev/null)
        echo "✅ 独立服务器 (端口 8940) - 运行中 (PID: $pid)"
        echo "   SSE 端点: http://localhost:8940/sse"
        echo "   健康检查: http://localhost:8940/health"
    else
        echo "❌ 独立服务器 (端口 8940) - 未运行"
    fi
    
    echo ""
    
    # 检查 Unity WebSocket 端口
    if check_port 8090; then
        echo "✅ Unity WebSocket (端口 8090) - 运行中"
    else
        echo "❌ Unity WebSocket (端口 8090) - 未运行"
        echo "   💡 请在 Unity Editor 中启动 MCP 服务器"
    fi
    
    echo ""
    echo "📋 客户端配置示例:"
    echo '   直接模式: {"command": "node", "args": ["'$(pwd)'/Unity-MCP/Server~/build/index.js"]}'
    echo '   独立模式: {"url": "http://localhost:8940/sse"}'
}

show_logs() {
    echo "📜 查看 Unity MCP 日志..."
    
    if [ -d "logs" ]; then
        echo "最近的日志文件:"
        ls -la logs/
        echo ""
        echo "最新日志内容:"
        tail -n 20 logs/*.log 2>/dev/null || echo "没有找到日志文件"
    else
        echo "日志目录不存在"
    fi
}

test_connection() {
    echo "🧪 测试 Unity MCP 连接..."
    
    # 测试独立服务器
    if check_port 8940; then
        echo "测试独立服务器健康检查..."
        if curl -s http://localhost:8940/health >/dev/null; then
            echo "✅ 独立服务器响应正常"
        else
            echo "❌ 独立服务器无响应"
        fi
    fi
    
    # 测试 Unity WebSocket
    if check_port 8090; then
        echo "✅ Unity WebSocket 端口开放"
    else
        echo "❌ Unity WebSocket 端口未开放"
        echo "   请确保在 Unity Editor 中启动了 MCP 服务器"
    fi
}

# 主程序
case $1 in
    "install")
        install_dependencies
        ;;
    "start")
        start_server $2
        ;;
    "stop")
        stop_server
        ;;
    "status")
        show_status
        ;;
    "restart")
        stop_server
        sleep 2
        start_server $2
        ;;
    "logs")
        show_logs
        ;;
    "test")
        test_connection
        ;;
    "help"|"-h"|"--help"|"")
        show_help
        ;;
    *)
        echo "❌ 未知命令: $1"
        echo "使用 '$0 help' 查看帮助"
        exit 1
        ;;
esac
