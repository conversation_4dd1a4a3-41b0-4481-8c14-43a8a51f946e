#!/bin/bash

# Unity MCP 网络配置脚本

echo "🌐 配置 Unity MCP 远程网络..."

# 获取服务器信息
SERVER_IP=$(hostname -I | awk '{print $1}')
if [ -z "$SERVER_IP" ]; then
    SERVER_IP=$(ip route get 1 | awk '{print $7; exit}')
fi

EXTERNAL_IP=$(curl -s ifconfig.me 2>/dev/null || echo "无法获取外网IP")

echo "📋 服务器网络信息:"
echo "   - 内网 IP: $SERVER_IP"
echo "   - 外网 IP: $EXTERNAL_IP"
echo "   - Unity WebSocket 端口: 8090"
echo "   - MCP 服务器端口: 8940"

# 检查端口状态
check_port() {
    local port=$1
    local name=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "✅ $name (端口 $port) - 正在监听"
        return 0
    else
        echo "❌ $name (端口 $port) - 未监听"
        return 1
    fi
}

echo ""
echo "🔍 检查端口状态:"
check_port 8090 "Unity WebSocket"
check_port 8940 "MCP 服务器"

# 配置防火墙
echo ""
echo "🔥 配置防火墙..."

if command -v ufw >/dev/null 2>&1; then
    echo "使用 UFW 防火墙:"
    
    # 检查 UFW 状态
    if ufw status | grep -q "Status: active"; then
        echo "   防火墙已启用，配置端口..."
        
        # 开放端口
        ufw allow 8090/tcp comment "Unity WebSocket"
        ufw allow 8940/tcp comment "Unity MCP Server"
        
        echo "   ✅ 端口已开放"
        
        # 显示规则
        echo "   当前规则:"
        ufw status numbered | grep -E "(8090|8940)"
    else
        echo "   防火墙未启用"
        echo "   如需启用: sudo ufw enable"
    fi
    
elif command -v firewall-cmd >/dev/null 2>&1; then
    echo "使用 Firewalld 防火墙:"
    
    # 开放端口
    firewall-cmd --permanent --add-port=8090/tcp
    firewall-cmd --permanent --add-port=8940/tcp
    firewall-cmd --reload
    
    echo "   ✅ 端口已开放"
    
elif command -v iptables >/dev/null 2>&1; then
    echo "使用 iptables 防火墙:"
    
    # 检查现有规则
    if iptables -L INPUT | grep -q "8090\|8940"; then
        echo "   端口规则已存在"
    else
        echo "   添加端口规则..."
        iptables -A INPUT -p tcp --dport 8090 -j ACCEPT
        iptables -A INPUT -p tcp --dport 8940 -j ACCEPT
        echo "   ✅ 端口规则已添加"
        echo "   💡 注意: 重启后规则会丢失，请保存规则"
    fi
    
else
    echo "   ⚠️  未检测到防火墙工具"
fi

# 测试端口连通性
echo ""
echo "🧪 测试端口连通性..."

test_port() {
    local port=$1
    local name=$2
    
    if nc -z localhost $port 2>/dev/null; then
        echo "✅ $name (端口 $port) - 本地连通"
    else
        echo "❌ $name (端口 $port) - 本地不通"
    fi
}

test_port 8090 "Unity WebSocket"
test_port 8940 "MCP 服务器"

# 生成连接配置
echo ""
echo "📝 生成连接配置..."

# Unity Editor 配置
cat > unity-editor-config.json << EOF
{
  "unityMcpSettings": {
    "websocketUrl": "ws://$SERVER_IP:8090",
    "autoReconnect": true,
    "reconnectDelay": 2000,
    "maxReconnectAttempts": 5,
    "requestTimeout": 10000
  }
}
EOF

# MCP 客户端配置
cat > mcp-client-remote-config.json << EOF
{
  "mcpServers": {
    "unity-mcp-remote": {
      "url": "http://$SERVER_IP:8940/sse",
      "description": "远程 Unity MCP 服务器"
    }
  },
  "serverInfo": {
    "serverIP": "$SERVER_IP",
    "externalIP": "$EXTERNAL_IP",
    "unityWebSocketPort": 8090,
    "mcpServerPort": 8940,
    "healthCheck": "http://$SERVER_IP:8940/health",
    "serverInfo": "http://$SERVER_IP:8940/info"
  }
}
EOF

echo "✅ 配置文件已生成:"
echo "   - unity-editor-config.json (Unity Editor 配置)"
echo "   - mcp-client-remote-config.json (MCP 客户端配置)"

# 显示连接信息
echo ""
echo "🔗 连接信息总结:"
echo ""
echo "📱 Unity Editor 连接:"
echo "   WebSocket URL: ws://$SERVER_IP:8090"
echo "   (如果外网访问: ws://$EXTERNAL_IP:8090)"
echo ""
echo "🖥️  MCP 客户端连接:"
echo "   SSE 端点: http://$SERVER_IP:8940/sse"
echo "   健康检查: http://$SERVER_IP:8940/health"
echo "   服务器信息: http://$SERVER_IP:8940/info"
echo "   (如果外网访问，将 IP 替换为: $EXTERNAL_IP)"
echo ""
echo "🔧 下一步操作:"
echo "1. 启动远程服务器: ./start-unity-remote.sh"
echo "2. 在本地 Unity Editor 中配置 WebSocket 连接"
echo "3. 在 MCP 客户端中添加远程服务器配置"
echo "4. 测试连接: curl http://$SERVER_IP:8940/health"

# 创建测试脚本
cat > test-remote-connection.sh << 'EOF'
#!/bin/bash

echo "🧪 测试远程连接..."

SERVER_IP=$(hostname -I | awk '{print $1}')

echo "测试 MCP 服务器健康检查..."
if curl -s "http://$SERVER_IP:8940/health" >/dev/null; then
    echo "✅ MCP 服务器响应正常"
    curl -s "http://$SERVER_IP:8940/health" | jq .
else
    echo "❌ MCP 服务器无响应"
fi

echo ""
echo "测试服务器信息端点..."
if curl -s "http://$SERVER_IP:8940/info" >/dev/null; then
    echo "✅ 服务器信息端点正常"
    curl -s "http://$SERVER_IP:8940/info" | jq .
else
    echo "❌ 服务器信息端点无响应"
fi

echo ""
echo "测试端口连通性..."
nc -z localhost 8090 && echo "✅ Unity WebSocket 端口 8090 开放" || echo "❌ Unity WebSocket 端口 8090 关闭"
nc -z localhost 8940 && echo "✅ MCP 服务器端口 8940 开放" || echo "❌ MCP 服务器端口 8940 关闭"
EOF

chmod +x test-remote-connection.sh

echo ""
echo "✅ 网络配置完成！"
echo "💡 使用 ./test-remote-connection.sh 测试连接"
