{"browser": {"browserName": "chromium", "isolated": false, "launchOptions": {"headless": false, "channel": "chrome"}, "contextOptions": {"viewport": {"width": 1280, "height": 720}, "ignoreHTTPSErrors": true}}, "server": {"port": 8931, "host": "localhost"}, "capabilities": ["core", "tabs", "pdf", "history", "wait", "files", "install", "testing"], "vision": false, "outputDir": "./playwright-output", "network": {"allowedOrigins": ["*"], "blockedOrigins": []}, "noImageResponses": false}