#!/bin/bash

echo "🧪 Unity MCP 完整功能测试..."

# 检查 Unity Editor 是否运行
check_unity() {
    if pgrep -f "Unity" >/dev/null; then
        echo "✅ Unity Editor 正在运行"
        return 0
    else
        echo "❌ Unity Editor 未运行"
        echo "💡 请先启动 Unity Editor"
        return 1
    fi
}

# 测试 Unity MCP 连接
test_mcp_connection() {
    echo "🔗 测试 Unity MCP 连接..."
    
    # 检查 WebSocket 端口
    if lsof -i :8090 >/dev/null 2>&1; then
        echo "✅ Unity WebSocket 端口 8090 已开放"
    else
        echo "❌ Unity WebSocket 端口 8090 未开放"
        echo "💡 请确保 Unity Editor 中的 MCP 插件已启用"
    fi
}

# 主测试流程
main() {
    echo "🚀 开始完整测试..."
    
    if check_unity; then
        test_mcp_connection
        
        echo ""
        echo "🎯 现在可以在 Cursor 中测试以下功能:"
        echo "1. @cursor 使用 Unity MCP 获取场景层级结构"
        echo "2. @cursor 使用 Unity MCP 创建一个新的游戏对象"
        echo "3. @cursor 使用 Unity MCP 查看控制台日志"
        echo "4. @cursor 使用 Unity MCP 执行菜单项 'GameObject/Create Empty'"
        echo ""
    else
        echo ""
        echo "📋 启动步骤:"
        echo "1. 连接 VNC: SERVER_IP:5901 (密码: unity123)"
        echo "2. 启动 Unity Hub: /opt/unity/start-unity-hub-vnc.sh"
        echo "3. 安装 Unity Editor (推荐 2022.3 LTS)"
        echo "4. 打开项目: /root/Desktop/MCP/Unity-Test-Project"
        echo "5. 确保 MCP 插件已启用"
        echo "6. 重新运行此测试"
    fi
}

main "$@"
