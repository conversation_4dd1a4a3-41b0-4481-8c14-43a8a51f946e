# 🖥️ Unity MCP 远程开发完整指南

## 🎯 远程开发场景分析

### 📊 **您的开发环境**
- **开发方式**: 远程开发（通过 SSH/VNC/远程桌面）
- **Unity Editor**: 需要在远程环境中运行
- **MCP 服务器**: 已部署在远程服务器上

### 🔄 **架构选择**

#### 选项 1：同机部署（推荐）
```
远程开发机器 (***************)
┌─────────────────────────────────────┐
│  ┌─────────────────┐  ┌─────────────┐ │
│  │   Unity Editor  │  │ Unity MCP   │ │
│  │   (VNC/RDP)     │  │ 服务器      │ │
│  │                 │  │             │ │
│  └─────────────────┘  └─────────────┘ │
│           │                  │        │
│           └──────────────────┘        │
│              localhost:8090           │
└─────────────────────────────────────────┘
                    │
                    │ MCP/SSE :8940
                    ▼
            ┌─────────────────┐
            │  MCP 客户端     │
            │  (本地/远程)    │
            └─────────────────┘
```

#### 选项 2：分离部署
```
远程开发机器A          远程服务器B (***************)
┌─────────────────┐    ┌──────────────────┐
│   Unity Editor  │    │  Unity MCP       │
│   (VNC/RDP)     │    │  服务器          │
│                 │    │                  │
└─────────────────┘    └──────────────────┘
         │                       │
         └───────────────────────┘
            WebSocket :8090
```

## 🚀 推荐方案：同机部署

### 1. **安装图形界面环境**

```bash
# 运行自动配置脚本
sudo ./remote-development-setup.sh
```

或手动安装：

```bash
# 安装桌面环境
sudo apt update
sudo apt install -y xfce4 xfce4-goodies
sudo apt install -y tightvncserver
sudo apt install -y firefox

# 安装 Unity 依赖
sudo apt install -y libgconf-2-4 libxss1 libglib2.0-0
sudo apt install -y libnss3 libxrandr2 libasound2 libpangocairo-1.0-0
sudo apt install -y libatk1.0-0 libcairo-gobject2 libgtk-3-0 libgdk-pixbuf2.0-0
```

### 2. **配置 VNC 远程桌面**

```bash
# 设置 VNC 密码
vncpasswd

# 启动 VNC 服务器
vncserver :1 -geometry 1920x1080 -depth 24

# 配置防火墙
sudo ufw allow 5901
```

**连接信息**：
- **VNC 地址**: `***************:5901`
- **分辨率**: 1920x1080

### 3. **安装 Unity Editor**

通过 VNC 连接到远程桌面后：

```bash
# 下载 Unity Hub
cd /opt
sudo mkdir unity
cd unity
sudo wget https://public-cdn.cloud.unity3d.com/hub/prod/UnityHub.AppImage
sudo chmod +x UnityHub.AppImage

# 启动 Unity Hub
export DISPLAY=:1
./UnityHub.AppImage --no-sandbox
```

### 4. **配置本地模式 Unity MCP**

```bash
# 使用本地模式配置
cd /root/Desktop/MCP
./start-unity-local.sh
```

**本地模式配置**：
- Unity WebSocket: `ws://localhost:8090`
- MCP 服务器: `http://localhost:8940/sse`

### 5. **MCP 客户端配置**

#### 在远程桌面内使用
```json
{
  "mcpServers": {
    "unity-mcp-local": {
      "url": "http://localhost:8940/sse"
    }
  }
}
```

#### 从外部访问（如果需要）
```json
{
  "mcpServers": {
    "unity-mcp-remote": {
      "url": "http://***************:8940/sse"
    }
  }
}
```

## 🔧 替代方案

### 方案 A：使用现有远程服务器

如果您已经有 Unity Editor 运行在其他远程机器上：

1. **修改 Unity MCP 配置**：
```bash
# 编辑配置文件
nano unity-mcp-config.json

# 修改 websocketHost 为您的 Unity Editor 机器 IP
{
  "unity": {
    "websocketHost": "YOUR_UNITY_EDITOR_IP",
    "websocketPort": 8090
  }
}
```

2. **网络配置**：
```bash
# 确保网络连通
ping YOUR_UNITY_EDITOR_IP

# 检查端口开放
telnet YOUR_UNITY_EDITOR_IP 8090
```

### 方案 B：端口转发

如果网络复杂，使用 SSH 端口转发：

```bash
# 在您的本地机器上
ssh -L 8090:localhost:8090 user@unity-editor-server
ssh -L 8940:localhost:8940 user@mcp-server

# 然后使用 localhost 连接
```

## 🧪 测试和验证

### 1. **测试 VNC 连接**
```bash
# 检查 VNC 服务器状态
vncserver -list

# 测试连接
# 使用 VNC 客户端连接到 ***************:5901
```

### 2. **测试 Unity MCP**
```bash
# 在远程桌面内测试
curl http://localhost:8940/health

# 检查 Unity WebSocket
netstat -ln | grep 8090
```

### 3. **测试完整流程**
1. 通过 VNC 连接到远程桌面
2. 启动 Unity Editor
3. 安装并配置 MCP 插件
4. 启动 Unity MCP 服务器
5. 在 MCP 客户端中测试功能

## 🚨 故障排除

### 常见问题

#### 1. **VNC 连接失败**
```bash
# 检查 VNC 服务器状态
ps aux | grep vnc

# 重启 VNC 服务器
vncserver -kill :1
vncserver :1 -geometry 1920x1080 -depth 24
```

#### 2. **Unity Editor 启动失败**
```bash
# 检查图形环境
echo $DISPLAY

# 设置显示
export DISPLAY=:1

# 检查依赖
ldd /opt/unity/UnityHub.AppImage
```

#### 3. **网络连接问题**
```bash
# 检查端口状态
lsof -i :8090
lsof -i :8940

# 检查防火墙
sudo ufw status
```

## 📊 性能优化

### 1. **VNC 优化**
```bash
# 使用压缩
vncserver :1 -geometry 1920x1080 -depth 16

# 或使用 NoVNC (Web 界面)
sudo apt install novnc
websockify --web=/usr/share/novnc/ 6080 localhost:5901
```

### 2. **Unity 优化**
- 降低图形质量设置
- 关闭不必要的窗口
- 使用轻量级项目模板

## 🎉 最终配置

### **推荐配置**：
1. ✅ 在同一台远程机器上运行 Unity Editor 和 MCP 服务器
2. ✅ 使用 VNC 进行图形界面访问
3. ✅ 使用本地模式连接（localhost）
4. ✅ 从外部通过 SSE 端点访问 MCP 功能

### **连接信息**：
- **VNC 远程桌面**: `***************:5901`
- **Unity WebSocket**: `ws://localhost:8090` (在远程桌面内)
- **MCP SSE 端点**: `http://***************:8940/sse` (外部访问)

这样配置后，您可以：
- 🖥️ 通过 VNC 远程操作 Unity Editor
- 🎮 在同一机器上运行 Unity MCP 服务器
- 🌐 从任何地方的 MCP 客户端访问 Unity 功能
- 🔧 享受完整的远程开发体验

现在您的远程开发环境完全兼容 Unity MCP 了！🚀
