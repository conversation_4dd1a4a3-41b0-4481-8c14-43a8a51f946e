#!/bin/bash

echo "🧪 测试 Unity Headless Mode..."

# 1. 检查 Unity 安装
echo "1. 检查 Unity 安装..."
if [ -f "/opt/unity/Unity-Headless" ] || [ -f "/opt/unity/Unity" ]; then
    echo "   ✅ Unity 可执行文件存在"
else
    echo "   ❌ Unity 可执行文件不存在"
    echo "   💡 运行安装: ./setup-unity-headless.sh"
    exit 1
fi

# 2. 检查项目
echo "2. 检查 Unity 项目..."
if [ -d "/root/Desktop/MCP/Unity-Test-Project" ]; then
    echo "   ✅ Unity 项目存在"
else
    echo "   ❌ Unity 项目不存在"
    exit 1
fi

# 3. 启动 Unity Headless
echo "3. 启动 Unity Headless..."
/opt/unity/manage-unity-headless.sh start

# 4. 等待启动
echo "4. 等待 Unity 启动..."
sleep 10

# 5. 检查状态
echo "5. 检查 Unity 状态..."
/opt/unity/manage-unity-headless.sh status

# 6. 启动 Unity MCP 模拟器
echo "6. 启动 Unity MCP 模拟器..."
if ! lsof -i :8090 >/dev/null 2>&1; then
    echo "   启动 Unity WebSocket 模拟器..."
    node unity-simulator.js &
    SIMULATOR_PID=$!
    sleep 3
fi

# 7. 测试 Unity MCP 连接
echo "7. 测试 Unity MCP 连接..."
if lsof -i :8090 >/dev/null 2>&1; then
    echo "   ✅ Unity WebSocket 端口 8090 已开放"
    
    echo "8. 测试 Cursor Unity MCP 功能..."
    echo "   现在可以在 Cursor 中测试:"
    echo "   @cursor 使用 Unity MCP 获取场景信息"
    echo "   @cursor 使用 Unity MCP 创建游戏对象"
    echo "   @cursor 使用 Unity MCP 查看日志"
else
    echo "   ❌ Unity WebSocket 端口 8090 未开放"
fi

echo ""
echo "🎯 管理命令:"
echo "   /opt/unity/manage-unity-headless.sh status   # 查看状态"
echo "   /opt/unity/manage-unity-headless.sh logs     # 查看日志"
echo "   /opt/unity/manage-unity-headless.sh stop     # 停止服务"
