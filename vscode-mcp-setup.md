# VS Code 中配置 Playwright MCP 服务

## 🎯 配置方法

### 方法一：使用 VS Code CLI（推荐）

在终端中运行以下命令：

```bash
# 基础配置
code --add-mcp '{"name":"playwright","command":"npx","args":["@playwright/mcp@latest"]}'

# 或者使用高级配置（无头模式）
code --add-mcp '{"name":"playwright-headless","command":"npx","args":["@playwright/mcp@latest","--headless","--browser","chrome"]}'

# 或者使用视觉模式
code --add-mcp '{"name":"playwright-vision","command":"npx","args":["@playwright/mcp@latest","--vision","--browser","chrome"]}'
```

### 方法二：手动配置文件

1. **找到 VS Code 配置目录**：
   - Windows: `%APPDATA%\Code\User\`
   - macOS: `~/Library/Application Support/Code/User/`
   - Linux: `~/.config/Code/User/`

2. **创建或编辑 `settings.json`**：
   在配置目录中找到 `settings.json` 文件，添加以下配置：

```json
{
  "mcp.servers": {
    "playwright": {
      "command": "npx",
      "args": ["@playwright/mcp@latest"]
    }
  }
}
```

### 方法三：通过 VS Code 设置界面

1. 打开 VS Code
2. 按 `Ctrl+,`（Windows/Linux）或 `Cmd+,`（macOS）打开设置
3. 搜索 "MCP"
4. 找到 "MCP: Servers" 设置
5. 点击 "Edit in settings.json"
6. 添加 Playwright 配置

## 🔧 配置选项详解

### 基础配置
```json
{
  "mcp.servers": {
    "playwright": {
      "command": "npx",
      "args": ["@playwright/mcp@latest"]
    }
  }
}
```

### 高级配置（多个实例）
```json
{
  "mcp.servers": {
    "playwright-basic": {
      "command": "npx",
      "args": ["@playwright/mcp@latest"]
    },
    "playwright-headless": {
      "command": "npx",
      "args": [
        "@playwright/mcp@latest",
        "--headless",
        "--browser", "chrome",
        "--output-dir", "./playwright-output"
      ]
    },
    "playwright-vision": {
      "command": "npx",
      "args": [
        "@playwright/mcp@latest",
        "--vision",
        "--browser", "chrome"
      ]
    },
    "playwright-firefox": {
      "command": "npx",
      "args": [
        "@playwright/mcp@latest",
        "--browser", "firefox"
      ]
    }
  }
}
```

### 自定义配置选项
```json
{
  "mcp.servers": {
    "playwright-custom": {
      "command": "npx",
      "args": [
        "@playwright/mcp@latest",
        "--browser", "chrome",
        "--headless",
        "--output-dir", "./playwright-output",
        "--save-trace",
        "--viewport-size", "1920,1080",
        "--user-agent", "Mozilla/5.0 (compatible; VS Code MCP Bot)",
        "--ignore-https-errors"
      ]
    }
  }
}
```

## 🚀 验证配置

### 1. 重启 VS Code
配置完成后，重启 VS Code 以加载新的 MCP 服务器。

### 2. 检查 MCP 状态
- 打开命令面板（`Ctrl+Shift+P` 或 `Cmd+Shift+P`）
- 搜索 "MCP"
- 查看可用的 MCP 相关命令

### 3. 测试 Playwright 工具
在 VS Code 中，您应该能够看到 Playwright 相关的工具和功能。

## 🛠️ 使用 GitHub Copilot 与 Playwright MCP

配置完成后，您可以在 VS Code 中使用 GitHub Copilot 与 Playwright MCP 交互：

### 示例对话
```
@copilot 请使用 Playwright 帮我：
1. 打开 https://www.example.com
2. 截取页面截图
3. 点击导航菜单
4. 保存页面为 PDF
```

### 常用命令
- `@copilot 使用 Playwright 打开网页`
- `@copilot 用 Playwright 截图`
- `@copilot 生成 Playwright 测试代码`
- `@copilot 用 Playwright 填写表单`

## 🔍 故障排除

### 常见问题

1. **MCP 服务器未启动**
   - 检查 Node.js 版本（需要 18+）
   - 确认网络连接正常
   - 重启 VS Code

2. **权限问题**
   ```bash
   # 在配置中添加 --no-sandbox
   "args": ["@playwright/mcp@latest", "--no-sandbox"]
   ```

3. **浏览器未安装**
   ```bash
   # 手动安装浏览器
   npx playwright install chrome
   ```

4. **配置文件位置**
   - 确认 settings.json 文件位置正确
   - 检查 JSON 语法是否正确

### 调试步骤

1. **查看 VS Code 输出**
   - 打开输出面板（`View > Output`）
   - 选择 "MCP" 频道查看日志

2. **测试命令行**
   ```bash
   # 直接测试 MCP 服务器
   npx @playwright/mcp@latest --help
   ```

3. **检查配置**
   ```bash
   # 验证配置文件
   code --list-extensions | grep -i mcp
   ```

## 📚 下一步

配置完成后，您可以：

1. **学习基础操作**：参考 `usage-examples.md`
2. **创建自动化脚本**：使用 Copilot 生成代码
3. **测试网站功能**：进行端到端测试
4. **数据抓取**：自动化数据收集任务

## 🎉 享受 Playwright MCP 的强大功能！

现在您可以在 VS Code 中使用 Playwright 进行强大的浏览器自动化操作了。
