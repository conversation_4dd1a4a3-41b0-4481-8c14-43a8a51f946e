#!/usr/bin/env node

/**
 * 测试 Playwright MCP 服务器
 * 这个脚本用于验证 Playwright MCP 服务器是否正常工作
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 开始测试 Playwright MCP 服务器...\n');

// 测试基本安装
console.log('1. 检查 Playwright MCP 包...');
const checkPackage = spawn('npx', ['@playwright/mcp@latest', '--help'], {
  stdio: 'pipe'
});

checkPackage.stdout.on('data', (data) => {
  console.log('✅ Playwright MCP 帮助信息:');
  console.log(data.toString());
});

checkPackage.stderr.on('data', (data) => {
  console.error('❌ 错误:', data.toString());
});

checkPackage.on('close', (code) => {
  if (code === 0) {
    console.log('✅ Playwright MCP 包检查完成\n');
    
    // 测试浏览器安装
    console.log('2. 安装浏览器依赖...');
    const installBrowser = spawn('npx', ['@playwright/mcp@latest', '--browser', 'chrome'], {
      stdio: 'inherit'
    });
    
    installBrowser.on('close', (installCode) => {
      if (installCode === 0) {
        console.log('✅ 浏览器安装完成');
      } else {
        console.log('⚠️  浏览器安装可能需要手动处理');
      }
      
      console.log('\n🎉 测试完成！');
      console.log('\n📋 下一步：');
      console.log('1. 将 mcp-config.json 配置添加到您的 MCP 客户端');
      console.log('2. 重启您的 MCP 客户端（如 VS Code、Claude Desktop 等）');
      console.log('3. 开始使用 Playwright 工具进行浏览器自动化');
    });
  } else {
    console.error('❌ Playwright MCP 包检查失败');
  }
});
