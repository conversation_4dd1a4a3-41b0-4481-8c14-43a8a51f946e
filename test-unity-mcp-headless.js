#!/usr/bin/env node

/**
 * Unity MCP Headless 模式测试脚本
 */

const WebSocket = require('ws');

console.log('🧪 测试 Unity MCP Headless 模式...');

// 测试 Unity WebSocket 连接
function testUnityWebSocket() {
    return new Promise((resolve) => {
        console.log('1. 测试 Unity WebSocket 连接...');
        
        const ws = new WebSocket('ws://localhost:8090/McpUnity');
        
        ws.on('open', () => {
            console.log('✅ Unity WebSocket 连接成功');
            
            // 发送测试请求
            const testRequest = {
                jsonrpc: '2.0',
                id: 'test-1',
                method: 'get_console_logs',
                params: { limit: 3, includeStackTrace: false }
            };
            
            console.log('📨 发送测试请求:', testRequest.method);
            ws.send(JSON.stringify(testRequest));
        });
        
        ws.on('message', (data) => {
            try {
                const response = JSON.parse(data.toString());
                console.log('📤 收到响应:', response);
                
                if (response.result) {
                    console.log('✅ Unity MCP 响应正常');
                    resolve(true);
                } else {
                    console.log('❌ Unity MCP 响应异常');
                    resolve(false);
                }
            } catch (error) {
                console.log('❌ 响应解析失败:', error.message);
                resolve(false);
            }
            
            ws.close();
        });
        
        ws.on('error', (error) => {
            console.log('❌ Unity WebSocket 连接失败:', error.message);
            resolve(false);
        });
        
        ws.on('close', () => {
            console.log('🔌 Unity WebSocket 连接已关闭');
        });
        
        // 超时处理
        setTimeout(() => {
            if (ws.readyState === WebSocket.CONNECTING) {
                console.log('⏰ Unity WebSocket 连接超时');
                ws.close();
                resolve(false);
            }
        }, 5000);
    });
}

// 测试多个 Unity MCP 功能
async function testUnityMCPFunctions() {
    console.log('2. 测试 Unity MCP 功能...');
    
    const testCases = [
        {
            name: '获取控制台日志',
            method: 'get_console_logs',
            params: { limit: 3, includeStackTrace: false }
        },
        {
            name: '获取场景层级',
            method: 'get_scenes_hierarchy',
            params: {}
        },
        {
            name: '创建游戏对象',
            method: 'create_gameobject',
            params: { name: 'TestObject' }
        },
        {
            name: '执行菜单项',
            method: 'execute_menu_item',
            params: { menuPath: 'GameObject/Create Empty' }
        },
        {
            name: '发送控制台日志',
            method: 'send_console_log',
            params: { message: 'Unity MCP Headless 测试成功！', type: 'info' }
        }
    ];
    
    let successCount = 0;
    
    for (const testCase of testCases) {
        const success = await testSingleFunction(testCase);
        if (success) {
            successCount++;
        }
        
        // 等待一下再测试下一个
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log(`📊 测试结果: ${successCount}/${testCases.length} 个功能正常`);
    return successCount === testCases.length;
}

// 测试单个功能
function testSingleFunction(testCase) {
    return new Promise((resolve) => {
        console.log(`   测试: ${testCase.name}...`);
        
        const ws = new WebSocket('ws://localhost:8090/McpUnity');
        
        ws.on('open', () => {
            const request = {
                jsonrpc: '2.0',
                id: `test-${Date.now()}`,
                method: testCase.method,
                params: testCase.params
            };
            
            ws.send(JSON.stringify(request));
        });
        
        ws.on('message', (data) => {
            try {
                const response = JSON.parse(data.toString());
                
                if (response.result) {
                    console.log(`   ✅ ${testCase.name} - 成功`);
                    resolve(true);
                } else if (response.error) {
                    console.log(`   ❌ ${testCase.name} - 错误: ${response.error.message}`);
                    resolve(false);
                } else {
                    console.log(`   ❌ ${testCase.name} - 未知响应`);
                    resolve(false);
                }
            } catch (error) {
                console.log(`   ❌ ${testCase.name} - 解析失败: ${error.message}`);
                resolve(false);
            }
            
            ws.close();
        });
        
        ws.on('error', (error) => {
            console.log(`   ❌ ${testCase.name} - 连接失败: ${error.message}`);
            resolve(false);
        });
        
        // 超时处理
        setTimeout(() => {
            if (ws.readyState !== WebSocket.CLOSED) {
                console.log(`   ⏰ ${testCase.name} - 超时`);
                ws.close();
                resolve(false);
            }
        }, 3000);
    });
}

// 主测试函数
async function runTests() {
    console.log('🚀 开始 Unity MCP Headless 测试...\n');
    
    // 测试基本连接
    const connectionOk = await testUnityWebSocket();
    console.log('');
    
    if (!connectionOk) {
        console.log('❌ Unity WebSocket 连接失败，无法继续测试');
        console.log('💡 请确保 Unity 模拟器正在运行: node unity-simulator.js');
        return;
    }
    
    // 测试 MCP 功能
    const functionsOk = await testUnityMCPFunctions();
    console.log('');
    
    // 总结
    console.log('📋 测试总结:');
    console.log('================================');
    console.log(`Unity WebSocket 连接: ${connectionOk ? '✅ 正常' : '❌ 失败'}`);
    console.log(`Unity MCP 功能: ${functionsOk ? '✅ 正常' : '❌ 部分失败'}`);
    console.log('');
    
    if (connectionOk && functionsOk) {
        console.log('🎉 Unity MCP Headless 模式测试通过！');
        console.log('');
        console.log('🎯 现在可以在 Cursor 中使用:');
        console.log('   @cursor 使用 Unity MCP 获取场景信息');
        console.log('   @cursor 使用 Unity MCP 创建游戏对象');
        console.log('   @cursor 使用 Unity MCP 查看控制台日志');
        console.log('');
        console.log('🔧 Cursor 配置:');
        console.log('   {"mcpServers": {"unity-mcp": {"command": "node", "args": ["/root/Desktop/MCP/Unity-MCP/Server~/build/index.js"], "env": {"UNITY_PORT": "8090", "UNITY_HOST": "localhost"}}}}');
    } else {
        console.log('❌ Unity MCP Headless 模式测试失败！');
        console.log('');
        console.log('🔧 故障排除:');
        console.log('   1. 检查 Unity 模拟器: curl http://localhost:8091/status');
        console.log('   2. 重启模拟器: node unity-simulator.js');
        console.log('   3. 检查端口占用: lsof -i :8090');
    }
}

// 运行测试
runTests().catch(console.error);
