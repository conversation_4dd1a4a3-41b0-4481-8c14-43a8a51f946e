#!/bin/bash

# Playwright MCP 服务器管理脚本

show_help() {
    echo "🎛️  Playwright MCP 服务器管理工具"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start [mode]     启动服务器"
    echo "  stop [port]      停止服务器"
    echo "  status           查看服务器状态"
    echo "  restart [mode]   重启服务器"
    echo "  logs [port]      查看服务器日志"
    echo ""
    echo "模式 (mode):"
    echo "  standard         标准模式 (端口 8931)"
    echo "  headless         无头模式 (端口 8932)"
    echo "  vision           视觉模式 (端口 8933)"
    echo "  all              启动所有模式"
    echo ""
    echo "示例:"
    echo "  $0 start standard    # 启动标准模式服务器"
    echo "  $0 stop 8931         # 停止端口 8931 上的服务器"
    echo "  $0 status            # 查看所有服务器状态"
}

check_port() {
    local port=$1
    if command -v lsof >/dev/null 2>&1; then
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            return 0  # 端口被占用
        else
            return 1  # 端口空闲
        fi
    else
        # 如果没有 lsof，使用 netstat
        if netstat -ln 2>/dev/null | grep -q ":$port "; then
            return 0
        else
            return 1
        fi
    fi
}

start_server() {
    local mode=$1
    
    case $mode in
        "standard")
            echo "🚀 启动标准模式服务器 (端口 8931)..."
            ./start-standalone-server.sh &
            ;;
        "headless")
            echo "🚀 启动无头模式服务器 (端口 8932)..."
            ./start-headless-server.sh &
            ;;
        "vision")
            echo "🚀 启动视觉模式服务器 (端口 8933)..."
            ./start-vision-server.sh &
            ;;
        "all")
            echo "🚀 启动所有服务器..."
            ./start-headless-server.sh &
            sleep 2
            ./start-vision-server.sh &
            sleep 2
            ./start-standalone-server.sh &
            ;;
        *)
            echo "❌ 未知模式: $mode"
            echo "可用模式: standard, headless, vision, all"
            exit 1
            ;;
    esac
}

stop_server() {
    local port=$1
    
    if [ -z "$port" ]; then
        echo "🛑 停止所有 Playwright MCP 服务器..."
        for p in 8931 8932 8933; do
            if check_port $p; then
                echo "   停止端口 $p 上的服务器..."
                lsof -ti:$p | xargs kill -9 2>/dev/null
            fi
        done
    else
        if check_port $port; then
            echo "🛑 停止端口 $port 上的服务器..."
            lsof -ti:$port | xargs kill -9
        else
            echo "⚠️  端口 $port 上没有运行的服务器"
        fi
    fi
}

show_status() {
    echo "📊 Playwright MCP 服务器状态:"
    echo ""
    
    local ports=(8931 8932 8933)
    local modes=("标准模式" "无头模式" "视觉模式")
    
    for i in "${!ports[@]}"; do
        local port=${ports[$i]}
        local mode=${modes[$i]}
        
        if check_port $port; then
            local pid=$(lsof -ti:$port)
            echo "✅ $mode (端口 $port) - 运行中 (PID: $pid)"
            echo "   SSE 端点: http://localhost:$port/sse"
        else
            echo "❌ $mode (端口 $port) - 未运行"
        fi
    done
    
    echo ""
    echo "📋 客户端配置示例:"
    echo '   "playwright": { "url": "http://localhost:8931/sse" }'
}

restart_server() {
    local mode=$1
    
    case $mode in
        "standard")
            stop_server 8931
            sleep 2
            start_server standard
            ;;
        "headless")
            stop_server 8932
            sleep 2
            start_server headless
            ;;
        "vision")
            stop_server 8933
            sleep 2
            start_server vision
            ;;
        "all")
            stop_server
            sleep 3
            start_server all
            ;;
        *)
            echo "❌ 未知模式: $mode"
            exit 1
            ;;
    esac
}

show_logs() {
    local port=$1
    
    if [ -z "$port" ]; then
        echo "📋 请指定端口号 (8931, 8932, 8933)"
        return 1
    fi
    
    if check_port $port; then
        local pid=$(lsof -ti:$port)
        echo "📜 查看端口 $port 服务器日志 (PID: $pid)..."
        echo "按 Ctrl+C 退出日志查看"
        tail -f /proc/$pid/fd/1 2>/dev/null || echo "无法访问日志文件"
    else
        echo "❌ 端口 $port 上没有运行的服务器"
    fi
}

# 主程序
case $1 in
    "start")
        start_server ${2:-standard}
        ;;
    "stop")
        stop_server $2
        ;;
    "status")
        show_status
        ;;
    "restart")
        restart_server ${2:-standard}
        ;;
    "logs")
        show_logs $2
        ;;
    "help"|"-h"|"--help"|"")
        show_help
        ;;
    *)
        echo "❌ 未知命令: $1"
        echo "使用 '$0 help' 查看帮助"
        exit 1
        ;;
esac
