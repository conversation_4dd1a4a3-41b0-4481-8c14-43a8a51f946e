{"server": {"name": "Unity MCP Server", "version": "1.0.0", "description": "MCP server for Unity Editor integration", "port": 8940, "host": "0.0.0.0"}, "unity": {"websocketPort": 8090, "websocketHost": "0.0.0.0", "requestTimeout": 10, "autoReconnect": true, "maxReconnectAttempts": 5, "reconnectDelay": 2000, "allowRemoteConnections": true}, "logging": {"level": "INFO", "enableFileLogging": true, "logDirectory": "./logs"}, "capabilities": {"tools": ["execute_menu_item", "select_gameobject", "update_gameobject", "update_component", "add_package", "run_tests", "send_console_log", "add_asset_to_scene"], "resources": ["unity://menu-items", "unity://scenes-hierarchy", "unity://gameobject/{id}", "unity://logs", "unity://packages", "unity://assets", "unity://tests/{testMode}"], "prompts": ["gameobject-handling"]}, "security": {"allowedOrigins": ["*"], "enableCors": true, "rateLimit": {"windowMs": 60000, "max": 100}}}