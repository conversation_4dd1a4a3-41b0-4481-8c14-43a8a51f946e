#!/bin/bash

# Unity 环境完整状态检查

echo "🔍 Unity 环境状态检查..."
echo "================================"

# 1. 检查 VNC 服务器
echo "🖥️ VNC 服务器状态:"
if pgrep -f "Xvnc.*:1" >/dev/null; then
    echo "   ✅ VNC 服务器运行中 (端口 5901)"
    echo "   🔗 连接地址: $(hostname -I | awk '{print $1}'):5901"
    echo "   🔑 密码: unity123"
else
    echo "   ❌ VNC 服务器未运行"
    echo "   💡 启动命令: vncserver :1 -geometry 1920x1080 -depth 24"
fi
echo ""

# 2. 检查 Unity Hub
echo "🎮 Unity Hub 状态:"
if [ -f "/opt/unity/UnityHub.AppImage" ]; then
    echo "   ✅ Unity Hub 已安装 ($(du -h /opt/unity/UnityHub.AppImage | cut -f1))"
    echo "   🚀 启动命令: /opt/unity/start-unity-hub-vnc.sh"
else
    echo "   ❌ Unity Hub 未安装"
fi
echo ""

# 3. 检查 Unity Editor
echo "🎯 Unity Editor 状态:"
UNITY_PROCESSES=$(pgrep -f "Unity" | grep -v "Unity-MCP" | wc -l)
if [ $UNITY_PROCESSES -gt 0 ]; then
    echo "   ✅ Unity 进程运行中 ($UNITY_PROCESSES 个进程)"
    ps aux | grep -E "(Unity|unity)" | grep -v grep | grep -v Unity-MCP | head -3
else
    echo "   ❌ Unity Editor 未运行"
fi

# 检查已安装的 Unity Editor
if [ -d "$HOME/Unity/Hub/Editor" ]; then
    echo "   📁 已安装的 Unity 版本:"
    ls -la "$HOME/Unity/Hub/Editor" 2>/dev/null | grep "^d" | awk '{print "      " $9}'
elif [ -f "/opt/unity/Unity" ]; then
    echo "   📁 Unity Editor 位置: /opt/unity/Unity"
else
    echo "   ❌ 未找到已安装的 Unity Editor"
fi
echo ""

# 4. 检查 Unity 项目
echo "📁 Unity 项目状态:"
if [ -d "/root/Desktop/MCP/Unity-Test-Project" ]; then
    echo "   ✅ 测试项目已创建"
    echo "   📂 路径: /root/Desktop/MCP/Unity-Test-Project"
    
    # 检查 MCP 插件
    if [ -d "/root/Desktop/MCP/Unity-Test-Project/Assets/Editor/MCP" ]; then
        echo "   ✅ MCP 插件已安装"
        echo "   📦 插件文件数: $(find /root/Desktop/MCP/Unity-Test-Project/Assets/Editor/MCP -type f | wc -l)"
    else
        echo "   ❌ MCP 插件未安装"
    fi
else
    echo "   ❌ 测试项目未创建"
fi
echo ""

# 5. 检查 Unity MCP 服务器
echo "🔧 Unity MCP 服务器状态:"
MCP_PROCESSES=$(pgrep -f "Unity-MCP" | wc -l)
if [ $MCP_PROCESSES -gt 0 ]; then
    echo "   ✅ Unity MCP 服务器运行中 ($MCP_PROCESSES 个进程)"
    
    # 检查端口
    if lsof -i :8090 >/dev/null 2>&1; then
        echo "   ✅ Unity WebSocket 端口 8090 已开放"
    else
        echo "   ❌ Unity WebSocket 端口 8090 未开放"
    fi
    
    if lsof -i :8940 >/dev/null 2>&1; then
        echo "   ✅ MCP 服务器端口 8940 已开放"
    else
        echo "   ❌ MCP 服务器端口 8940 未开放"
    fi
else
    echo "   ❌ Unity MCP 服务器未运行"
fi
echo ""

# 6. 检查网络连接
echo "🌐 网络连接状态:"
echo "   📡 服务器 IP: $(hostname -I | awk '{print $1}')"
echo "   🔗 VNC 连接: $(hostname -I | awk '{print $1}'):5901"
if lsof -i :8940 >/dev/null 2>&1; then
    echo "   🔗 MCP 连接: http://$(hostname -I | awk '{print $1}'):8940/sse"
fi
echo ""

# 7. 总结和建议
echo "📋 下一步建议:"
echo "================================"

if ! pgrep -f "Xvnc.*:1" >/dev/null; then
    echo "1. 🖥️ 启动 VNC 服务器: vncserver :1 -geometry 1920x1080 -depth 24"
fi

if [ $UNITY_PROCESSES -eq 0 ]; then
    echo "2. 🎮 通过 VNC 启动 Unity Hub: /opt/unity/start-unity-hub-vnc.sh"
    echo "3. 📥 在 Unity Hub 中安装 Unity Editor 2022.3 LTS"
    echo "4. 📂 打开项目: /root/Desktop/MCP/Unity-Test-Project"
fi

if [ $MCP_PROCESSES -eq 0 ]; then
    echo "5. 🔧 启动 Unity MCP 服务器"
fi

if ! lsof -i :8090 >/dev/null 2>&1 && [ $UNITY_PROCESSES -gt 0 ]; then
    echo "6. 🔌 在 Unity Editor 中启用 MCP 插件"
fi

echo ""
echo "🧪 测试命令:"
echo "   ./test-unity-mcp-complete.sh    # 测试 MCP 连接"
echo "   ./install-unity-editor-simple.sh # 安装 Unity Editor"
echo ""

echo "🎯 Cursor 配置:"
echo '   {"mcpServers": {"unity-mcp": {"command": "node", "args": ["/root/Desktop/MCP/Unity-MCP/Server~/build/index.js"], "env": {"UNITY_PORT": "8090", "UNITY_HOST": "localhost"}}}}'
echo ""
