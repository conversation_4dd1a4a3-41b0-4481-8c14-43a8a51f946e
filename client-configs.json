{"standalone_servers": {"description": "独立服务器配置 - 适用于任何 MCP 客户端", "servers": {"playwright-standalone": {"url": "http://localhost:8931/sse", "description": "标准模式 - 有头浏览器"}, "playwright-headless": {"url": "http://localhost:8932/sse", "description": "无头模式 - 后台运行"}, "playwright-vision": {"url": "http://localhost:8933/sse", "description": "视觉模式 - 支持坐标点击"}}}, "vscode_config": {"mcpServers": {"playwright-standalone": {"url": "http://localhost:8931/sse"}, "playwright-headless": {"url": "http://localhost:8932/sse"}, "playwright-vision": {"url": "http://localhost:8933/sse"}}}, "claude_desktop_config": {"mcpServers": {"playwright-standalone": {"url": "http://localhost:8931/sse"}, "playwright-headless": {"url": "http://localhost:8932/sse"}, "playwright-vision": {"url": "http://localhost:8933/sse"}}}, "cursor_config": {"mcpServers": {"playwright-standalone": {"url": "http://localhost:8931/sse"}, "playwright-headless": {"url": "http://localhost:8932/sse"}, "playwright-vision": {"url": "http://localhost:8933/sse"}}}, "windsurf_config": {"mcpServers": {"playwright-standalone": {"url": "http://localhost:8931/sse"}, "playwright-headless": {"url": "http://localhost:8932/sse"}, "playwright-vision": {"url": "http://localhost:8933/sse"}}}}