#!/bin/bash

# Unity 完整安装和测试脚本

echo "🎮 设置完整的 Unity MCP 测试环境..."

# 获取服务器 IP
SERVER_IP=$(hostname -I | awk '{print $1}')
echo "📋 服务器 IP: $SERVER_IP"

# 1. 启动 VNC 服务器（如果未运行）
start_vnc() {
    if ! pgrep -f "Xvnc.*:1" >/dev/null; then
        echo "🖥️ 启动 VNC 服务器..."
        vncserver :1 -geometry 1920x1080 -depth 24
    else
        echo "✅ VNC 服务器已运行"
    fi
}

# 2. 创建 Unity 项目结构
create_unity_project() {
    echo "📁 创建 Unity 项目结构..."
    
    cd /root/Desktop/MCP
    
    # 创建基本项目结构
    mkdir -p Unity-Test-Project/{Assets/{Editor/MCP,Scripts,Scenes},ProjectSettings,Packages}
    
    # 复制 MCP 插件
    if [ -d "Unity-MCP/Editor" ]; then
        cp -r Unity-MCP/Editor/* Unity-Test-Project/Assets/Editor/MCP/
        echo "✅ MCP 插件已复制"
    fi
    
    # 创建基本的项目设置
    cat > Unity-Test-Project/ProjectSettings/ProjectVersion.txt << 'EOF'
m_EditorVersion: 2022.3.45f1
m_EditorVersionWithRevision: 2022.3.45f1 (c2d5a7410213)
EOF

    # 创建包管理器配置
    cat > Unity-Test-Project/Packages/manifest.json << 'EOF'
{
  "dependencies": {
    "com.unity.collab-proxy": "2.0.5",
    "com.unity.feature.development": "1.0.1",
    "com.unity.textmeshpro": "3.0.6",
    "com.unity.timeline": "1.7.5",
    "com.unity.ugui": "1.0.0",
    "com.unity.visualscripting": "1.8.0",
    "com.unity.modules.ai": "1.0.0",
    "com.unity.modules.androidjni": "1.0.0",
    "com.unity.modules.animation": "1.0.0",
    "com.unity.modules.assetbundle": "1.0.0",
    "com.unity.modules.audio": "1.0.0",
    "com.unity.modules.cloth": "1.0.0",
    "com.unity.modules.director": "1.0.0",
    "com.unity.modules.imageconversion": "1.0.0",
    "com.unity.modules.imgui": "1.0.0",
    "com.unity.modules.jsonserialize": "1.0.0",
    "com.unity.modules.particlesystem": "1.0.0",
    "com.unity.modules.physics": "1.0.0",
    "com.unity.modules.physics2d": "1.0.0",
    "com.unity.modules.screencapture": "1.0.0",
    "com.unity.modules.terrain": "1.0.0",
    "com.unity.modules.terrainphysics": "1.0.0",
    "com.unity.modules.tilemap": "1.0.0",
    "com.unity.modules.ui": "1.0.0",
    "com.unity.modules.uielements": "1.0.0",
    "com.unity.modules.umbra": "1.0.0",
    "com.unity.modules.unityanalytics": "1.0.0",
    "com.unity.modules.unitywebrequest": "1.0.0",
    "com.unity.modules.unitywebrequestassetbundle": "1.0.0",
    "com.unity.modules.unitywebrequestexture": "1.0.0",
    "com.unity.modules.unitywebrequesttexture": "1.0.0",
    "com.unity.modules.unitywebrequestwww": "1.0.0",
    "com.unity.modules.vehicles": "1.0.0",
    "com.unity.modules.video": "1.0.0",
    "com.unity.modules.vr": "1.0.0",
    "com.unity.modules.wind": "1.0.0",
    "com.unity.modules.xr": "1.0.0"
  }
}
EOF

    echo "✅ Unity 项目结构创建完成"
}

# 3. 创建 Unity 启动脚本
create_unity_scripts() {
    echo "📝 创建 Unity 启动脚本..."
    
    # Unity Hub 启动脚本
    cat > /opt/unity/start-unity-hub-vnc.sh << 'EOF'
#!/bin/bash
export DISPLAY=:1
cd /opt/unity
echo "🎮 启动 Unity Hub..."
echo "📋 项目路径: /root/Desktop/MCP/Unity-Test-Project"
./UnityHub.AppImage --no-sandbox --disable-gpu-sandbox --disable-software-rasterizer
EOF
    chmod +x /opt/unity/start-unity-hub-vnc.sh
    
    # Unity Editor 直接启动脚本（如果有的话）
    cat > /opt/unity/start-unity-editor.sh << 'EOF'
#!/bin/bash
export DISPLAY=:1

# 查找 Unity Editor
UNITY_EDITOR=""
if [ -d "/opt/unity/Editor" ]; then
    UNITY_EDITOR="/opt/unity/Editor/Unity"
elif [ -d "$HOME/Unity/Hub/Editor" ]; then
    # 查找最新版本
    UNITY_EDITOR=$(find "$HOME/Unity/Hub/Editor" -name "Unity" -type f | head -1)
fi

if [ -n "$UNITY_EDITOR" ] && [ -f "$UNITY_EDITOR" ]; then
    echo "🎮 启动 Unity Editor: $UNITY_EDITOR"
    echo "📁 项目路径: /root/Desktop/MCP/Unity-Test-Project"
    "$UNITY_EDITOR" -projectPath "/root/Desktop/MCP/Unity-Test-Project"
else
    echo "❌ Unity Editor 未找到"
    echo "💡 请先通过 Unity Hub 安装 Unity Editor"
    echo "🚀 启动 Unity Hub: /opt/unity/start-unity-hub-vnc.sh"
fi
EOF
    chmod +x /opt/unity/start-unity-editor.sh
    
    echo "✅ Unity 启动脚本创建完成"
}

# 4. 创建测试脚本
create_test_script() {
    echo "🧪 创建 Unity MCP 测试脚本..."
    
    cat > /root/Desktop/MCP/test-unity-mcp-complete.sh << 'EOF'
#!/bin/bash

echo "🧪 Unity MCP 完整功能测试..."

# 检查 Unity Editor 是否运行
check_unity() {
    if pgrep -f "Unity" >/dev/null; then
        echo "✅ Unity Editor 正在运行"
        return 0
    else
        echo "❌ Unity Editor 未运行"
        echo "💡 请先启动 Unity Editor"
        return 1
    fi
}

# 测试 Unity MCP 连接
test_mcp_connection() {
    echo "🔗 测试 Unity MCP 连接..."
    
    # 检查 WebSocket 端口
    if lsof -i :8090 >/dev/null 2>&1; then
        echo "✅ Unity WebSocket 端口 8090 已开放"
    else
        echo "❌ Unity WebSocket 端口 8090 未开放"
        echo "💡 请确保 Unity Editor 中的 MCP 插件已启用"
    fi
}

# 主测试流程
main() {
    echo "🚀 开始完整测试..."
    
    if check_unity; then
        test_mcp_connection
        
        echo ""
        echo "🎯 现在可以在 Cursor 中测试以下功能:"
        echo "1. @cursor 使用 Unity MCP 获取场景层级结构"
        echo "2. @cursor 使用 Unity MCP 创建一个新的游戏对象"
        echo "3. @cursor 使用 Unity MCP 查看控制台日志"
        echo "4. @cursor 使用 Unity MCP 执行菜单项 'GameObject/Create Empty'"
        echo ""
    else
        echo ""
        echo "📋 启动步骤:"
        echo "1. 连接 VNC: SERVER_IP:5901 (密码: unity123)"
        echo "2. 启动 Unity Hub: /opt/unity/start-unity-hub-vnc.sh"
        echo "3. 安装 Unity Editor (推荐 2022.3 LTS)"
        echo "4. 打开项目: /root/Desktop/MCP/Unity-Test-Project"
        echo "5. 确保 MCP 插件已启用"
        echo "6. 重新运行此测试"
    fi
}

main "$@"
EOF
    chmod +x /root/Desktop/MCP/test-unity-mcp-complete.sh
    
    echo "✅ 测试脚本创建完成"
}

# 5. 创建使用指南
create_guide() {
    echo "📚 创建使用指南..."
    
    cat > /root/Desktop/MCP/UNITY-COMPLETE-SETUP.md << EOF
# 🎮 Unity Editor + MCP 完整安装指南

## 🎉 环境准备完成！

### 📊 当前状态
- ✅ VNC 服务器运行中 (端口 5901)
- ✅ Unity Hub 已安装
- ✅ Unity 项目结构已创建
- ✅ Unity MCP 插件已配置
- ✅ 测试脚本已准备

### 🔗 连接信息
- **VNC 地址**: $SERVER_IP:5901
- **VNC 密码**: unity123
- **项目路径**: /root/Desktop/MCP/Unity-Test-Project

## 🚀 安装步骤

### 1. 连接 VNC 桌面
使用 VNC 客户端连接到: **$SERVER_IP:5901**

### 2. 启动 Unity Hub
在 VNC 桌面中打开终端，运行:
\`\`\`bash
/opt/unity/start-unity-hub-vnc.sh
\`\`\`

### 3. 安装 Unity Editor
1. 在 Unity Hub 中点击 "Installs"
2. 点击 "Install Editor"
3. 选择 "Unity 2022.3.45f1 LTS" (推荐)
4. 等待安装完成

### 4. 打开测试项目
1. 在 Unity Hub 中点击 "Projects"
2. 点击 "Open"
3. 选择路径: \`/root/Desktop/MCP/Unity-Test-Project\`
4. 等待项目加载

### 5. 启用 MCP 插件
1. 在 Unity Editor 中，检查 \`Assets/Editor/MCP\` 目录
2. 确保插件文件已正确加载
3. 查看 Console 窗口是否有错误

## 🧪 测试 Unity MCP

### 运行测试脚本
\`\`\`bash
cd /root/Desktop/MCP
./test-unity-mcp-complete.sh
\`\`\`

### 在 Cursor 中测试
配置 Cursor MCP:
\`\`\`json
{
  "mcpServers": {
    "unity-mcp": {
      "command": "node",
      "args": ["/root/Desktop/MCP/Unity-MCP/Server~/build/index.js"],
      "env": {
        "UNITY_PORT": "8090",
        "UNITY_HOST": "localhost",
        "UNITY_REQUEST_TIMEOUT": "10",
        "LOGGING": "true"
      }
    }
  }
}
\`\`\`

### 测试命令
\`\`\`
@cursor 使用 Unity MCP 获取场景层级结构
@cursor 使用 Unity MCP 创建一个名为 "TestCube" 的游戏对象
@cursor 使用 Unity MCP 查看控制台日志
@cursor 使用 Unity MCP 执行菜单项 "GameObject/3D Object/Cube"
\`\`\`

## 🔧 管理命令

### VNC 管理
\`\`\`bash
vncserver -list                    # 查看 VNC 状态
vncserver -kill :1                 # 停止 VNC
vncserver :1 -geometry 1920x1080   # 重启 VNC
\`\`\`

### Unity 管理
\`\`\`bash
/opt/unity/start-unity-hub-vnc.sh  # 启动 Unity Hub
/opt/unity/start-unity-editor.sh   # 直接启动 Unity Editor
\`\`\`

### MCP 管理
\`\`\`bash
./test-unity-mcp-complete.sh       # 测试 MCP 连接
./manage-unity-mcp.sh status       # 查看 MCP 状态
\`\`\`

## 🎯 成功标志

安装成功后，您应该能够:
- ✅ 通过 VNC 访问图形界面
- ✅ 启动 Unity Hub 和 Unity Editor
- ✅ 打开 Unity 测试项目
- ✅ 在 Cursor 中使用 Unity MCP 工具
- ✅ 实时操作 Unity 场景和对象

## 🚨 故障排除

### Unity Hub 启动失败
\`\`\`bash
export DISPLAY=:1
cd /opt/unity
./UnityHub.AppImage --no-sandbox
\`\`\`

### Unity Editor 无法连接 MCP
1. 检查 MCP 插件是否正确安装
2. 查看 Unity Console 是否有错误
3. 确认端口 8090 未被占用

### VNC 连接问题
\`\`\`bash
vncserver -kill :1
vncserver :1 -geometry 1920x1080 -depth 24
\`\`\`

现在您拥有了完整的 Unity Editor + MCP 开发环境！🚀
EOF

    echo "✅ 使用指南创建完成"
}

# 主程序
main() {
    start_vnc
    create_unity_project
    create_unity_scripts
    create_test_script
    create_guide
    
    echo ""
    echo "🎉 Unity 完整环境设置完成！"
    echo ""
    echo "📋 下一步操作:"
    echo "1. 连接 VNC: $SERVER_IP:5901 (密码: unity123)"
    echo "2. 启动 Unity Hub: /opt/unity/start-unity-hub-vnc.sh"
    echo "3. 安装 Unity Editor 2022.3 LTS"
    echo "4. 打开项目: /root/Desktop/MCP/Unity-Test-Project"
    echo "5. 测试 MCP: ./test-unity-mcp-complete.sh"
    echo ""
    echo "📚 详细说明: UNITY-COMPLETE-SETUP.md"
}

# 运行主程序
main "$@"
