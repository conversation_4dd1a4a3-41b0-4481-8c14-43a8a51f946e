# 🌐 Unity MCP 通用服务器完整指南

## 🎉 **已完成配置**

Unity MCP 通用服务器已成功部署，支持**任何 IDE 和编辑器**！

### ✅ **服务器状态**
- **🌐 通用 MCP 服务器**: 运行在 `http://***************:8940/sse`
- **🎮 Unity 模拟器**: 运行在 `ws://localhost:8090/McpUnity`
- **🔧 健康检查**: `http://***************:8940/health`
- **📋 服务信息**: `http://***************:8940/info`

## 🎯 **支持的 IDE 和编辑器**

### ✅ **主流 AI 编程工具**
- **Cursor** - AI 代码编辑器
- **VS Code** - 微软代码编辑器 (需要 MCP 扩展)
- **Claude Desktop** - Anthropic 桌面应用
- **Continue.dev** - AI 编程助手
- **Codeium** - AI 代码补全
- **GitHub Copilot** - GitHub AI 助手

### 🔧 **传统编辑器**
- **JetBrains IDEs** (IntelliJ, PyCharm, WebStorm 等)
- **Neovim** - 现代 Vim
- **Emacs** - GNU Emacs
- **Sublime Text** - 轻量级编辑器
- **Vim** - 经典编辑器
- **Atom** - GitHub 编辑器

### 🌐 **通用客户端**
- **任何支持 HTTP/SSE 的应用**
- **自定义脚本和工具**
- **Web 应用程序**
- **移动应用**

## 🚀 **快速配置指南**

### **Cursor (推荐)**
1. 打开 Cursor 设置
2. 找到 MCP 配置
3. 使用以下配置：
```json
{
  "mcpServers": {
    "unity-mcp": {
      "url": "http://***************:8940/sse"
    }
  }
}
```

### **VS Code**
1. 安装 MCP 扩展
2. 在设置中添加：
```json
{
  "mcp.servers": {
    "unity-mcp": {
      "url": "http://***************:8940/sse",
      "name": "Unity MCP Server"
    }
  }
}
```

### **Claude Desktop**
1. 打开 Claude Desktop 配置
2. 添加：
```json
{
  "mcpServers": {
    "unity-mcp": {
      "url": "http://***************:8940/sse"
    }
  }
}
```

### **其他 IDE**
查看 `ide-configs/` 目录中对应的配置文件：
- `cursor-config.json` - Cursor
- `vscode-settings.json` - VS Code
- `jetbrains-config.xml` - JetBrains IDEs
- `neovim-config.lua` - Neovim
- `emacs-config.el` - Emacs
- 等等...

## 🎮 **Unity MCP 功能**

### **可用工具**
1. **execute_menu_item** - 执行 Unity 菜单项
2. **get_console_logs** - 获取控制台日志
3. **send_console_log** - 发送日志到控制台
4. **create_gameobject** - 创建游戏对象
5. **get_scenes_hierarchy** - 获取场景层级
6. **select_gameobject** - 选择游戏对象
7. **update_gameobject** - 更新游戏对象
8. **add_component** - 添加组件

### **可用资源**
1. **unity://scenes** - Unity 场景信息
2. **unity://hierarchy** - 场景层级结构
3. **unity://logs** - 控制台日志
4. **unity://status** - Unity 连接状态

## 🧪 **使用示例**

配置完成后，在任何支持的 IDE 中：

```
@assistant 使用 Unity MCP 获取场景层级结构
@assistant 使用 Unity MCP 创建一个名为 "Player" 的游戏对象
@assistant 使用 Unity MCP 查看控制台日志
@assistant 使用 Unity MCP 执行菜单项 "GameObject/3D Object/Cube"
@assistant 使用 Unity MCP 为 Player 对象添加 Rigidbody 组件
```

## 🔧 **管理命令**

### **服务器管理**
```bash
# 启动服务器
cd /root/Desktop/MCP
node unity-mcp-universal-server.js

# 检查状态
curl http://***************:8940/health

# 查看服务信息
curl http://***************:8940/info

# 停止服务器
Ctrl+C
```

### **Unity 管理**
```bash
# 启动 Unity 模拟器
node unity-simulator.js

# 检查 Unity 连接
curl http://localhost:8091/status

# 查看端口状态
lsof -i :8090
lsof -i :8940
```

## 🌐 **网络配置**

### **本地访问**
- **服务器地址**: `http://localhost:8940/sse`
- **适用于**: 同一台机器上的 IDE

### **远程访问**
- **服务器地址**: `http://***************:8940/sse`
- **适用于**: 任何网络位置的 IDE
- **防火墙**: 确保端口 8940 已开放

### **Docker 部署**
```bash
cd ide-configs
docker-compose up -d
```

## 🔍 **故障排除**

### **连接失败**
1. **检查服务器状态**:
   ```bash
   curl http://***************:8940/health
   ```

2. **检查端口占用**:
   ```bash
   lsof -i :8940
   ```

3. **查看服务器日志**:
   检查运行 `node unity-mcp-universal-server.js` 的终端输出

### **Unity 连接失败**
1. **检查 Unity 模拟器**:
   ```bash
   curl http://localhost:8091/status
   ```

2. **重启 Unity 模拟器**:
   ```bash
   pkill -f unity-simulator
   node unity-simulator.js
   ```

### **IDE 配置问题**
1. **确认 IDE 支持 MCP**
2. **检查配置文件格式**
3. **重启 IDE 应用配置**
4. **查看 IDE 错误日志**

## 🎯 **架构优势**

### **通用性**
- ✅ **任何 IDE** - 支持所有 MCP 兼容的编辑器
- ✅ **跨平台** - Windows, macOS, Linux
- ✅ **远程访问** - 不受地理位置限制

### **可扩展性**
- ✅ **多客户端** - 同时支持多个 IDE 连接
- ✅ **负载均衡** - 可以部署多个服务器实例
- ✅ **集群部署** - 支持 Docker 和 Kubernetes

### **开发效率**
- ✅ **统一接口** - 所有 IDE 使用相同的 Unity 功能
- ✅ **实时同步** - 多个开发者协同工作
- ✅ **自动化** - 支持 CI/CD 集成

## 📊 **性能特点**

| 特性 | 通用服务器 | 直接集成 |
|------|------------|----------|
| **IDE 支持** | ✅ 任何 IDE | ❌ 特定 IDE |
| **部署复杂度** | ✅ 简单 | ❌ 复杂 |
| **维护成本** | ✅ 低 | ❌ 高 |
| **扩展性** | ✅ 优秀 | ❌ 有限 |
| **性能** | ✅ 高 | ✅ 高 |

## 🎉 **总结**

**Unity MCP 通用服务器已完全配置完成！**

现在您可以：
- 🎮 **在任何 IDE 中使用 Unity 功能**
- 🌐 **从任何地方远程访问**
- 🔧 **享受完整的 Unity 自动化**
- 🚀 **提升开发效率**

**Unity 开发从未如此简单和通用！** 🚀

---

### 📞 **技术支持**

如果遇到问题：
1. 查看服务器日志
2. 检查网络连接
3. 验证 IDE 配置
4. 确认 Unity 状态

**现在开始在您喜欢的 IDE 中享受 Unity MCP 功能吧！** 🎉
