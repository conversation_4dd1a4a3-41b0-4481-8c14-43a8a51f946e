# Microsoft Playwright MCP 服务器配置

这个项目包含了 Microsoft Playwright MCP (Model Context Protocol) 服务器的完整配置和使用示例。

## 📋 目录结构

```
/root/Desktop/MCP/
├── mcp-config.json              # 基础 MCP 配置
├── mcp-config-advanced.json     # 高级 MCP 配置（多个服务器实例）
├── playwright-mcp-config.json   # Playwright 详细配置
├── test-playwright-mcp.js       # 测试脚本
└── README.md                    # 本文档
```

## 🚀 快速开始

### 1. 环境要求

- Node.js 18 或更新版本 ✅ (当前: v22.16.0)
- npm 或 yarn
- 支持的 MCP 客户端（VS Code、Claude Desktop、Cursor、Windsurf 等）

### 2. 基础配置

使用 `mcp-config.json` 进行基础配置：

```json
{
  "mcpServers": {
    "playwright": {
      "command": "npx",
      "args": ["@playwright/mcp@latest"]
    }
  }
}
```

### 3. 高级配置

使用 `mcp-config-advanced.json` 获得更多功能：

- **playwright**: 无头模式，适合自动化
- **playwright-headed**: 有头模式，适合调试
- **playwright-vision**: 视觉模式，支持截图交互

## 🔧 配置选项

### 浏览器选项
- `--browser chrome|firefox|webkit|msedge`: 选择浏览器
- `--headless`: 无头模式
- `--device "iPhone 15"`: 设备模拟

### 网络选项
- `--allowed-origins`: 允许的域名
- `--blocked-origins`: 阻止的域名
- `--proxy-server`: 代理服务器

### 输出选项
- `--output-dir`: 输出目录
- `--save-trace`: 保存 Playwright 跟踪
- `--no-image-responses`: 不发送图片响应

## 🛠️ 可用工具

### 核心工具
- `browser_snapshot`: 获取页面可访问性快照
- `browser_navigate`: 导航到 URL
- `browser_click`: 点击元素
- `browser_type`: 输入文本
- `browser_take_screenshot`: 截图

### 高级工具
- `browser_drag`: 拖拽操作
- `browser_file_upload`: 文件上传
- `browser_pdf_save`: 保存为 PDF
- `browser_generate_playwright_test`: 生成测试代码

### 标签页管理
- `browser_tab_list`: 列出标签页
- `browser_tab_new`: 新建标签页
- `browser_tab_select`: 选择标签页
- `browser_tab_close`: 关闭标签页

## 🧪 测试

运行测试脚本验证配置：

```bash
node test-playwright-mcp.js
```

## 📱 客户端配置

### VS Code
```bash
code --add-mcp '{"name":"playwright","command":"npx","args":["@playwright/mcp@latest"]}'
```

### Claude Desktop
将配置添加到 Claude Desktop 的配置文件中。

### Cursor
在 Cursor Settings -> MCP -> Add new MCP Server 中添加配置。

## 🔍 使用示例

### 基础网页自动化
```
请帮我：
1. 打开 https://example.com
2. 截取页面截图
3. 点击"联系我们"链接
4. 填写联系表单
```

### 数据抓取
```
请访问 https://news.ycombinator.com 并：
1. 获取首页所有文章标题
2. 保存为 PDF
3. 生成相应的 Playwright 测试代码
```

### 测试生成
```
请为以下场景生成 Playwright 测试：
1. 用户登录流程
2. 购物车添加商品
3. 结账流程
```

## 🚨 故障排除

### 常见问题

1. **浏览器未安装**
   ```bash
   npx @playwright/mcp@latest --browser chrome
   ```

2. **权限问题**
   ```bash
   npx @playwright/mcp@latest --no-sandbox
   ```

3. **网络问题**
   ```bash
   npx @playwright/mcp@latest --ignore-https-errors
   ```

## 📚 更多资源

- [Playwright 官方文档](https://playwright.dev)
- [MCP 协议文档](https://modelcontextprotocol.io)
- [GitHub 仓库](https://github.com/microsoft/playwright-mcp)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

Apache 2.0 License
