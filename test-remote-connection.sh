#!/bin/bash

echo "🧪 测试远程连接..."

SERVER_IP=$(hostname -I | awk '{print $1}')

echo "测试 MCP 服务器健康检查..."
if curl -s "http://$SERVER_IP:8940/health" >/dev/null; then
    echo "✅ MCP 服务器响应正常"
    curl -s "http://$SERVER_IP:8940/health" | jq .
else
    echo "❌ MCP 服务器无响应"
fi

echo ""
echo "测试服务器信息端点..."
if curl -s "http://$SERVER_IP:8940/info" >/dev/null; then
    echo "✅ 服务器信息端点正常"
    curl -s "http://$SERVER_IP:8940/info" | jq .
else
    echo "❌ 服务器信息端点无响应"
fi

echo ""
echo "测试端口连通性..."
nc -z localhost 8090 && echo "✅ Unity WebSocket 端口 8090 开放" || echo "❌ Unity WebSocket 端口 8090 关闭"
nc -z localhost 8940 && echo "✅ MCP 服务器端口 8940 开放" || echo "❌ MCP 服务器端口 8940 关闭"
