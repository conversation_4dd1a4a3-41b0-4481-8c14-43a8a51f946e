# Playwright MCP 使用示例

## 🎯 基础使用场景

### 1. 网页截图和导航
```
请帮我：
1. 打开 https://www.baidu.com
2. 截取页面截图
3. 搜索"人工智能"
4. 再次截图保存结果
```

### 2. 表单填写和提交
```
请访问一个联系表单页面，并帮我：
1. 填写姓名字段
2. 填写邮箱地址
3. 填写消息内容
4. 提交表单
5. 截图确认提交成功
```

### 3. 数据抓取
```
请访问 https://news.ycombinator.com 并：
1. 获取首页前10个新闻标题
2. 点击第一个新闻链接
3. 保存页面为PDF
4. 返回首页
```

## 🔧 高级功能示例

### 4. 多标签页操作
```
请帮我：
1. 打开3个新标签页
2. 分别访问不同的网站
3. 在每个标签页截图
4. 切换回第一个标签页
5. 关闭其他标签页
```

### 5. 文件上传测试
```
请找一个文件上传页面，然后：
1. 选择文件上传控件
2. 上传一个测试文件
3. 确认上传成功
4. 截图保存结果
```

### 6. 网络请求监控
```
请访问一个网站并：
1. 开始监控网络请求
2. 执行一些页面操作
3. 查看所有网络请求
4. 分析请求类型和响应
```

## 🧪 测试代码生成

### 7. 自动化测试生成
```
请为以下用户流程生成 Playwright 测试代码：
1. 用户打开登录页面
2. 输入用户名和密码
3. 点击登录按钮
4. 验证登录成功
5. 退出登录
```

### 8. 购物流程测试
```
请生成一个电商网站的测试，包括：
1. 浏览商品列表
2. 选择商品加入购物车
3. 查看购物车
4. 进入结账流程
5. 填写配送信息
```

## 📱 移动设备模拟

### 9. 移动端测试
```
请使用 iPhone 15 设备模拟：
1. 访问一个响应式网站
2. 测试移动端菜单
3. 测试触摸滑动
4. 截图对比桌面版差异
```

### 10. 不同设备对比
```
请分别使用以下设备访问同一网站：
1. iPhone 15
2. iPad
3. 桌面浏览器
4. 对比截图差异
```

## 🎨 视觉模式示例

### 11. 坐标点击（Vision Mode）
```
启用视觉模式，然后：
1. 截取页面截图
2. 根据截图坐标点击特定位置
3. 拖拽元素到新位置
4. 验证操作结果
```

### 12. 复杂交互（Vision Mode）
```
使用视觉模式进行：
1. 图片轮播操作
2. 拖拽排序
3. 画布绘制
4. 复杂表单交互
```

## 🔍 调试和分析

### 13. 性能分析
```
请访问一个网站并：
1. 监控页面加载时间
2. 分析网络请求性能
3. 检查控制台错误
4. 生成性能报告
```

### 14. 可访问性检查
```
请检查网站的可访问性：
1. 获取页面可访问性快照
2. 检查键盘导航
3. 验证屏幕阅读器兼容性
4. 生成可访问性报告
```

## 🚀 实际应用场景

### 15. 竞品分析
```
请帮我分析竞争对手网站：
1. 访问多个竞品网站
2. 截图保存关键页面
3. 分析页面结构
4. 对比功能差异
```

### 16. 网站监控
```
请设置网站健康检查：
1. 定期访问关键页面
2. 检查页面是否正常加载
3. 验证关键功能可用性
4. 记录异常情况
```

## 💡 提示和技巧

### 最佳实践
1. **使用描述性元素名称**：便于理解和维护
2. **合理使用等待**：确保页面完全加载
3. **错误处理**：处理弹窗和异常情况
4. **截图记录**：保存操作过程和结果

### 常用命令组合
- 导航 + 截图 + 操作 + 验证
- 多标签页切换 + 数据对比
- 表单填写 + 提交 + 确认
- 文件操作 + 上传 + 验证

### 调试技巧
- 使用有头模式观察操作过程
- 保存 Playwright 跟踪文件
- 监控网络请求和控制台日志
- 使用视觉模式处理复杂交互
