# 🎮 Unity Editor + MCP 完整安装指南

## 🎉 环境准备完成！

### 📊 当前状态
- ✅ VNC 服务器运行中 (端口 5901)
- ✅ Unity Hub 已安装
- ✅ Unity 项目结构已创建
- ✅ Unity MCP 插件已配置
- ✅ 测试脚本已准备

### 🔗 连接信息
- **VNC 地址**: 103.144.245.201:5901
- **VNC 密码**: unity123
- **项目路径**: /root/Desktop/MCP/Unity-Test-Project

## 🚀 安装步骤

### 1. 连接 VNC 桌面
使用 VNC 客户端连接到: **103.144.245.201:5901**

### 2. 启动 Unity Hub
在 VNC 桌面中打开终端，运行:
```bash
/opt/unity/start-unity-hub-vnc.sh
```

### 3. 安装 Unity Editor
1. 在 Unity Hub 中点击 "Installs"
2. 点击 "Install Editor"
3. 选择 "Unity 2022.3.45f1 LTS" (推荐)
4. 等待安装完成

### 4. 打开测试项目
1. 在 Unity Hub 中点击 "Projects"
2. 点击 "Open"
3. 选择路径: `/root/Desktop/MCP/Unity-Test-Project`
4. 等待项目加载

### 5. 启用 MCP 插件
1. 在 Unity Editor 中，检查 `Assets/Editor/MCP` 目录
2. 确保插件文件已正确加载
3. 查看 Console 窗口是否有错误

## 🧪 测试 Unity MCP

### 运行测试脚本
```bash
cd /root/Desktop/MCP
./test-unity-mcp-complete.sh
```

### 在 Cursor 中测试
配置 Cursor MCP:
```json
{
  "mcpServers": {
    "unity-mcp": {
      "command": "node",
      "args": ["/root/Desktop/MCP/Unity-MCP/Server~/build/index.js"],
      "env": {
        "UNITY_PORT": "8090",
        "UNITY_HOST": "localhost",
        "UNITY_REQUEST_TIMEOUT": "10",
        "LOGGING": "true"
      }
    }
  }
}
```

### 测试命令
```
@cursor 使用 Unity MCP 获取场景层级结构
@cursor 使用 Unity MCP 创建一个名为 "TestCube" 的游戏对象
@cursor 使用 Unity MCP 查看控制台日志
@cursor 使用 Unity MCP 执行菜单项 "GameObject/3D Object/Cube"
```

## 🔧 管理命令

### VNC 管理
```bash
vncserver -list                    # 查看 VNC 状态
vncserver -kill :1                 # 停止 VNC
vncserver :1 -geometry 1920x1080   # 重启 VNC
```

### Unity 管理
```bash
/opt/unity/start-unity-hub-vnc.sh  # 启动 Unity Hub
/opt/unity/start-unity-editor.sh   # 直接启动 Unity Editor
```

### MCP 管理
```bash
./test-unity-mcp-complete.sh       # 测试 MCP 连接
./manage-unity-mcp.sh status       # 查看 MCP 状态
```

## 🎯 成功标志

安装成功后，您应该能够:
- ✅ 通过 VNC 访问图形界面
- ✅ 启动 Unity Hub 和 Unity Editor
- ✅ 打开 Unity 测试项目
- ✅ 在 Cursor 中使用 Unity MCP 工具
- ✅ 实时操作 Unity 场景和对象

## 🚨 故障排除

### Unity Hub 启动失败
```bash
export DISPLAY=:1
cd /opt/unity
./UnityHub.AppImage --no-sandbox
```

### Unity Editor 无法连接 MCP
1. 检查 MCP 插件是否正确安装
2. 查看 Unity Console 是否有错误
3. 确认端口 8090 未被占用

### VNC 连接问题
```bash
vncserver -kill :1
vncserver :1 -geometry 1920x1080 -depth 24
```

现在您拥有了完整的 Unity Editor + MCP 开发环境！🚀
