#!/bin/bash

# Unity 完整安装和配置脚本

echo "🎮 开始安装 Unity 和配置 MCP 环境..."

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用 root 权限运行此脚本"
    exit 1
fi

# 获取服务器 IP
SERVER_IP=$(hostname -I | awk '{print $1}')
echo "📋 服务器 IP: $SERVER_IP"

# 1. 安装基础依赖
install_dependencies() {
    echo "📦 安装基础依赖..."
    
    # 更新包列表
    apt update
    
    # 安装图形界面（如果未安装）
    if ! command -v startxfce4 >/dev/null 2>&1; then
        echo "安装 XFCE 桌面环境..."
        apt install -y xfce4 xfce4-goodies
    fi
    
    # 安装 VNC 服务器
    if ! command -v vncserver >/dev/null 2>&1; then
        echo "安装 VNC 服务器..."
        apt install -y tightvncserver
    fi
    
    # 安装 Unity 依赖
    echo "安装 Unity 依赖..."
    apt install -y \
        libgconf-2-4 libxss1 libglib2.0-0 \
        libnss3 libxrandr2 libasound2 libpangocairo-1.0-0 \
        libatk1.0-0 libcairo-gobject2 libgtk-3-0 libgdk-pixbuf2.0-0 \
        libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 \
        libxi6 libxrender1 libxtst6 libxss1 libgconf-2-4 \
        libasound2-dev libgtk2.0-0 libgtk-3-0 libgbm-dev \
        wget curl unzip
    
    echo "✅ 基础依赖安装完成"
}

# 2. 创建 Unity 目录和下载 Unity Hub
install_unity_hub() {
    echo "🎮 安装 Unity Hub..."
    
    # 创建 Unity 目录
    mkdir -p /opt/unity
    cd /opt/unity
    
    # 下载 Unity Hub
    echo "下载 Unity Hub..."
    wget -O UnityHub.AppImage "https://public-cdn.cloud.unity3d.com/hub/prod/UnityHub.AppImage"
    chmod +x UnityHub.AppImage
    
    # 创建启动脚本
    cat > start-unity-hub.sh << 'EOF'
#!/bin/bash
export DISPLAY=:1
cd /opt/unity
./UnityHub.AppImage --no-sandbox --disable-gpu-sandbox --disable-software-rasterizer
EOF
    chmod +x start-unity-hub.sh
    
    echo "✅ Unity Hub 安装完成"
}

# 3. 配置 VNC 服务器
setup_vnc() {
    echo "🖥️ 配置 VNC 服务器..."
    
    # 创建 VNC 配置目录
    mkdir -p /root/.vnc
    
    # 设置默认 VNC 密码（用户可以后续修改）
    echo "unity123" | vncpasswd -f > /root/.vnc/passwd
    chmod 600 /root/.vnc/passwd
    
    # 创建 VNC 启动配置
    cat > /root/.vnc/xstartup << 'EOF'
#!/bin/bash
xrdb $HOME/.Xresources
startxfce4 &
EOF
    chmod +x /root/.vnc/xstartup
    
    # 创建 VNC 管理脚本
    cat > /opt/unity/manage-vnc.sh << 'EOF'
#!/bin/bash

case $1 in
    "start")
        echo "🖥️ 启动 VNC 服务器..."
        vncserver :1 -geometry 1920x1080 -depth 24
        echo "✅ VNC 服务器已启动"
        echo "📱 连接信息: SERVER_IP:5901"
        echo "🔑 默认密码: unity123"
        ;;
    "stop")
        echo "🛑 停止 VNC 服务器..."
        vncserver -kill :1
        ;;
    "restart")
        echo "🔄 重启 VNC 服务器..."
        vncserver -kill :1 2>/dev/null
        sleep 2
        vncserver :1 -geometry 1920x1080 -depth 24
        ;;
    "status")
        if pgrep -f "Xvnc.*:1" >/dev/null; then
            echo "✅ VNC 服务器运行中"
            echo "📱 连接地址: SERVER_IP:5901"
        else
            echo "❌ VNC 服务器未运行"
        fi
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status}"
        ;;
esac
EOF
    
    # 替换 SERVER_IP
    sed -i "s/SERVER_IP/$SERVER_IP/g" /opt/unity/manage-vnc.sh
    chmod +x /opt/unity/manage-vnc.sh
    
    echo "✅ VNC 服务器配置完成"
}

# 4. 配置 Unity MCP 本地模式
setup_unity_mcp() {
    echo "🔧 配置 Unity MCP..."
    
    cd /root/Desktop/MCP
    
    # 确保 Unity MCP 已构建
    if [ ! -f "Unity-MCP/Server~/build/index.js" ]; then
        echo "❌ Unity MCP 未构建，请先运行构建"
        return 1
    fi
    
    # 创建完整的启动脚本
    cat > start-unity-complete.sh << 'EOF'
#!/bin/bash

echo "🎮 启动完整的 Unity 开发环境..."

# 1. 启动 VNC 服务器
echo "1. 启动 VNC 服务器..."
/opt/unity/manage-vnc.sh start

# 2. 等待 VNC 启动
sleep 3

# 3. 启动 Unity MCP 本地模式
echo "2. 启动 Unity MCP 服务器..."
cd /root/Desktop/MCP
./start-unity-local.sh &
MCP_PID=$!

echo ""
echo "🎉 Unity 开发环境启动完成！"
echo ""
echo "📱 VNC 连接信息:"
echo "   地址: SERVER_IP:5901"
echo "   密码: unity123"
echo ""
echo "🎮 Unity Hub 启动:"
echo "   在 VNC 桌面中运行: /opt/unity/start-unity-hub.sh"
echo ""
echo "🔗 MCP 连接信息:"
echo "   本地: http://localhost:8940/sse"
echo "   远程: http://SERVER_IP:8940/sse"
echo ""
echo "💡 使用说明:"
echo "1. 使用 VNC 客户端连接到 SERVER_IP:5901"
echo "2. 在 VNC 桌面中启动 Unity Hub"
echo "3. 安装 Unity Editor 并创建项目"
echo "4. 安装 Unity MCP 插件"
echo "5. 在 MCP 客户端中配置连接"
echo ""
echo "按 Ctrl+C 停止服务..."

# 等待中断信号
trap 'echo "🛑 停止服务..."; kill $MCP_PID 2>/dev/null; /opt/unity/manage-vnc.sh stop; exit 0' INT

wait $MCP_PID
EOF
    
    # 替换 SERVER_IP
    sed -i "s/SERVER_IP/$SERVER_IP/g" start-unity-complete.sh
    chmod +x start-unity-complete.sh
    
    echo "✅ Unity MCP 配置完成"
}

# 5. 创建使用指南
create_guide() {
    echo "📚 创建使用指南..."
    
    cat > /root/Desktop/MCP/UNITY-INSTALLATION-COMPLETE.md << EOF
# 🎮 Unity 安装完成指南

## 🎉 安装状态：完成！

Unity 开发环境已成功安装并配置完成。

## 📊 环境信息

- **服务器 IP**: $SERVER_IP
- **VNC 端口**: 5901
- **VNC 密码**: unity123
- **Unity MCP 端口**: 8940
- **Unity WebSocket 端口**: 8090

## 🚀 快速启动

### 1. 启动完整环境
\`\`\`bash
cd /root/Desktop/MCP
./start-unity-complete.sh
\`\`\`

### 2. 连接 VNC 桌面
- **地址**: $SERVER_IP:5901
- **密码**: unity123
- **推荐客户端**: RealVNC Viewer, TightVNC

### 3. 在 VNC 桌面中启动 Unity Hub
\`\`\`bash
/opt/unity/start-unity-hub.sh
\`\`\`

## 🔧 管理命令

### VNC 管理
\`\`\`bash
/opt/unity/manage-vnc.sh start    # 启动 VNC
/opt/unity/manage-vnc.sh stop     # 停止 VNC
/opt/unity/manage-vnc.sh status   # 查看状态
\`\`\`

### Unity MCP 管理
\`\`\`bash
./manage-unity-mcp.sh status      # 查看状态
./start-unity-local.sh            # 启动本地模式
\`\`\`

## 📱 MCP 客户端配置

### 本地模式（在服务器内部）
\`\`\`json
{
  "mcpServers": {
    "unity-mcp": {
      "url": "http://localhost:8940/sse"
    }
  }
}
\`\`\`

### 远程模式（外部访问）
\`\`\`json
{
  "mcpServers": {
    "unity-mcp": {
      "url": "http://$SERVER_IP:8940/sse"
    }
  }
}
\`\`\`

## 🎯 使用流程

1. **启动环境**: \`./start-unity-complete.sh\`
2. **连接 VNC**: 使用 VNC 客户端连接
3. **启动 Unity Hub**: 在 VNC 桌面中运行
4. **安装 Unity Editor**: 通过 Unity Hub 安装
5. **创建项目**: 新建或打开 Unity 项目
6. **安装 MCP 插件**: 复制插件到项目
7. **配置连接**: 在 Unity Editor 中配置 WebSocket
8. **使用 MCP**: 在客户端中享受 Unity 自动化

## 🔗 重要链接

- **健康检查**: http://$SERVER_IP:8940/health
- **服务器信息**: http://$SERVER_IP:8940/info
- **Unity Hub 路径**: /opt/unity/UnityHub.AppImage

## 🚨 故障排除

### VNC 连接问题
\`\`\`bash
# 重启 VNC 服务器
/opt/unity/manage-vnc.sh restart

# 检查端口
lsof -i :5901
\`\`\`

### Unity Hub 启动问题
\`\`\`bash
# 检查显示环境
echo \$DISPLAY

# 手动设置显示
export DISPLAY=:1
\`\`\`

### MCP 连接问题
\`\`\`bash
# 测试连接
curl http://localhost:8940/health

# 查看日志
./manage-unity-mcp.sh logs
\`\`\`

现在您可以开始使用完整的 Unity MCP 开发环境了！🚀
EOF

    echo "✅ 使用指南创建完成"
}

# 主程序
main() {
    echo "🚀 开始完整安装..."
    
    install_dependencies
    install_unity_hub
    setup_vnc
    setup_unity_mcp
    create_guide
    
    echo ""
    echo "🎉 Unity 安装和配置完成！"
    echo ""
    echo "📋 下一步操作："
    echo "1. 启动完整环境: ./start-unity-complete.sh"
    echo "2. 连接 VNC: $SERVER_IP:5901 (密码: unity123)"
    echo "3. 在 VNC 中启动 Unity Hub"
    echo "4. 安装 Unity Editor 并开始开发"
    echo ""
    echo "📚 详细说明请查看: UNITY-INSTALLATION-COMPLETE.md"
}

# 运行主程序
main "$@"
