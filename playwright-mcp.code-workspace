{"folders": [{"path": "."}], "settings": {"mcp.servers": {"playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "playwright-headless": {"command": "npx", "args": ["@playwright/mcp@latest", "--headless", "--browser", "chrome", "--output-dir", "./playwright-output", "--save-trace"]}, "playwright-vision": {"command": "npx", "args": ["@playwright/mcp@latest", "--vision", "--browser", "chrome"]}}}, "extensions": {"recommendations": ["ms-vscode.vscode-copilot", "ms-vscode.vscode-copilot-chat"]}}