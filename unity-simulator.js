#!/usr/bin/env node

/**
 * Unity WebSocket 模拟器
 * 用于在没有 Unity Editor 的情况下测试 Unity MCP
 */

const WebSocket = require('ws');
const express = require('express');

console.log('🎮 启动 Unity WebSocket 模拟器...');

// 创建 WebSocket 服务器（模拟 Unity Editor）
const wss = new WebSocket.Server({ 
  port: 8090,
  host: 'localhost'
});

console.log('📡 Unity WebSocket 模拟器运行在: ws://localhost:8090');

// 模拟 Unity 场景数据
const mockUnityData = {
  scenes: [
    {
      name: "SampleScene",
      path: "Assets/Scenes/SampleScene.unity",
      gameObjects: [
        {
          id: "1",
          name: "Main Camera",
          tag: "MainCamera",
          active: true,
          transform: {
            position: { x: 0, y: 1, z: -10 },
            rotation: { x: 0, y: 0, z: 0 },
            scale: { x: 1, y: 1, z: 1 }
          },
          components: ["Camera", "AudioListener"]
        },
        {
          id: "2", 
          name: "Directional Light",
          tag: "Untagged",
          active: true,
          transform: {
            position: { x: 0, y: 3, z: 0 },
            rotation: { x: 50, y: -30, z: 0 },
            scale: { x: 1, y: 1, z: 1 }
          },
          components: ["Light"]
        }
      ]
    }
  ],
  packages: [
    { name: "com.unity.ugui", version: "1.0.0" },
    { name: "com.unity.textmeshpro", version: "3.0.6" }
  ],
  logs: [
    { level: "Info", message: "Unity MCP 模拟器已启动", timestamp: new Date().toISOString() },
    { level: "Info", message: "场景 SampleScene 已加载", timestamp: new Date().toISOString() }
  ]
};

wss.on('connection', (ws, req) => {
  console.log(`🔗 新的连接来自: ${req.socket.remoteAddress}`);
  
  // 发送欢迎消息
  ws.send(JSON.stringify({
    type: 'welcome',
    message: 'Unity MCP 模拟器已连接',
    version: '1.0.0'
  }));
  
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message.toString());
      console.log('📨 收到消息:', data);
      
      // 处理不同类型的请求
      switch (data.type) {
        case 'get_scenes':
          ws.send(JSON.stringify({
            type: 'scenes_response',
            data: mockUnityData.scenes
          }));
          break;
          
        case 'get_gameobjects':
          const scene = mockUnityData.scenes[0];
          ws.send(JSON.stringify({
            type: 'gameobjects_response',
            data: scene.gameObjects
          }));
          break;
          
        case 'create_gameobject':
          const newObj = {
            id: String(Date.now()),
            name: data.name || 'New GameObject',
            tag: 'Untagged',
            active: true,
            transform: {
              position: { x: 0, y: 0, z: 0 },
              rotation: { x: 0, y: 0, z: 0 },
              scale: { x: 1, y: 1, z: 1 }
            },
            components: []
          };
          mockUnityData.scenes[0].gameObjects.push(newObj);
          ws.send(JSON.stringify({
            type: 'gameobject_created',
            data: newObj
          }));
          break;
          
        case 'get_packages':
          ws.send(JSON.stringify({
            type: 'packages_response',
            data: mockUnityData.packages
          }));
          break;
          
        case 'get_logs':
          ws.send(JSON.stringify({
            type: 'logs_response',
            data: mockUnityData.logs
          }));
          break;
          
        case 'execute_menu':
          mockUnityData.logs.push({
            level: 'Info',
            message: `执行菜单项: ${data.menuItem}`,
            timestamp: new Date().toISOString()
          });
          ws.send(JSON.stringify({
            type: 'menu_executed',
            success: true,
            menuItem: data.menuItem
          }));
          break;
          
        default:
          ws.send(JSON.stringify({
            type: 'error',
            message: `未知请求类型: ${data.type}`
          }));
      }
    } catch (error) {
      console.error('❌ 处理消息错误:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: error.message
      }));
    }
  });
  
  ws.on('close', () => {
    console.log('🔌 连接已断开');
  });
  
  ws.on('error', (error) => {
    console.error('❌ WebSocket 错误:', error);
  });
});

// 创建状态检查服务器
const app = express();
app.use(express.json());

app.get('/status', (req, res) => {
  res.json({
    status: 'running',
    service: 'Unity WebSocket 模拟器',
    port: 8090,
    connections: wss.clients.size,
    mockData: {
      scenes: mockUnityData.scenes.length,
      gameObjects: mockUnityData.scenes[0].gameObjects.length,
      packages: mockUnityData.packages.length,
      logs: mockUnityData.logs.length
    }
  });
});

app.listen(8091, () => {
  console.log('🏥 状态检查服务器运行在: http://localhost:8091/status');
});

console.log('');
console.log('🎯 使用说明:');
console.log('1. Unity MCP 服务器会连接到 ws://localhost:8090');
console.log('2. 模拟器会响应 Unity MCP 的请求');
console.log('3. 查看状态: curl http://localhost:8091/status');
console.log('4. 在 Cursor 中配置 Unity MCP 即可使用');
console.log('');

process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭 Unity 模拟器...');
  wss.close();
  process.exit(0);
});
