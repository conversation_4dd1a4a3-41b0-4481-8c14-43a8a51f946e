#!/bin/bash

# Unity MCP 远程开发环境配置脚本

echo "🖥️ 配置 Unity MCP 远程开发环境..."

# 检查是否有图形界面
check_gui() {
    if [ -n "$DISPLAY" ]; then
        echo "✅ 检测到图形界面环境: $DISPLAY"
        return 0
    elif command -v Xvfb >/dev/null 2>&1; then
        echo "✅ 检测到 Xvfb 虚拟显示"
        return 0
    else
        echo "❌ 未检测到图形界面环境"
        return 1
    fi
}

# 安装图形界面支持
install_gui_support() {
    echo "📦 安装图形界面支持..."
    
    # 更新包列表
    apt update
    
    # 安装 X11 和桌面环境
    apt install -y xfce4 xfce4-goodies
    apt install -y xrdp
    apt install -y firefox
    
    # 安装 VNC 服务器
    apt install -y tightvncserver
    
    # 安装 Xvfb (虚拟显示)
    apt install -y xvfb
    
    echo "✅ 图形界面支持安装完成"
}

# 配置 Unity 远程访问
setup_unity_remote() {
    echo "🎮 配置 Unity 远程访问..."
    
    # 创建 Unity 安装目录
    mkdir -p /opt/unity
    cd /opt/unity
    
    # 下载 Unity Hub (Linux)
    echo "📥 下载 Unity Hub..."
    wget -q https://public-cdn.cloud.unity3d.com/hub/prod/UnityHub.AppImage
    chmod +x UnityHub.AppImage
    
    # 创建启动脚本
    cat > start-unity-hub.sh << 'EOF'
#!/bin/bash
export DISPLAY=:1
cd /opt/unity
./UnityHub.AppImage --no-sandbox
EOF
    chmod +x start-unity-hub.sh
    
    echo "✅ Unity Hub 配置完成"
}

# 配置本地连接模式
setup_local_mode() {
    echo "🔧 配置本地连接模式..."
    
    # 修改 Unity MCP 配置为本地模式
    cat > /root/Desktop/MCP/unity-local-config.json << EOF
{
  "server": {
    "name": "Unity MCP Server (Local)",
    "version": "1.0.0", 
    "description": "MCP server for local Unity Editor",
    "port": 8940,
    "host": "localhost"
  },
  "unity": {
    "websocketPort": 8090,
    "websocketHost": "localhost",
    "requestTimeout": 10,
    "autoReconnect": true,
    "maxReconnectAttempts": 5,
    "reconnectDelay": 2000,
    "allowRemoteConnections": false
  },
  "logging": {
    "level": "INFO",
    "enableFileLogging": true,
    "logDirectory": "./logs"
  }
}
EOF

    # 创建本地模式启动脚本
    cat > /root/Desktop/MCP/start-unity-local.sh << 'EOF'
#!/bin/bash

echo "🎮 启动 Unity MCP 本地模式..."

# 检查 Unity Editor 是否运行
if ! pgrep -f "Unity" >/dev/null; then
    echo "⚠️  Unity Editor 未运行，请先启动 Unity Editor"
    echo "💡 使用: /opt/unity/start-unity-hub.sh"
fi

cd /root/Desktop/MCP/Unity-MCP/Server~

# 设置环境变量
export UNITY_PORT=8090
export UNITY_HOST=localhost
export UNITY_REQUEST_TIMEOUT=10
export LOGGING=true
export MCP_SERVER_PORT=8940
export MCP_SERVER_HOST=localhost

echo "📋 本地模式配置:"
echo "   - Unity WebSocket: ws://localhost:8090"
echo "   - MCP 服务器: http://localhost:8940/sse"

# 启动本地模式服务器
node build/index.js
EOF

    chmod +x /root/Desktop/MCP/start-unity-local.sh
    
    echo "✅ 本地模式配置完成"
}

# 配置 VNC 访问
setup_vnc() {
    echo "🖥️ 配置 VNC 远程桌面..."
    
    # 创建 VNC 启动脚本
    cat > start-vnc.sh << 'EOF'
#!/bin/bash

# 启动 VNC 服务器
vncserver :1 -geometry 1920x1080 -depth 24

echo "🖥️ VNC 服务器已启动"
echo "📱 连接信息:"
echo "   地址: YOUR_SERVER_IP:5901"
echo "   密码: 请设置 VNC 密码"
echo ""
echo "💡 设置密码: vncpasswd"
echo "💡 停止服务: vncserver -kill :1"
EOF

    chmod +x start-vnc.sh
    
    echo "✅ VNC 配置完成"
}

# 主程序
main() {
    echo "🚀 开始配置远程开发环境..."
    
    # 检查权限
    if [ "$EUID" -ne 0 ]; then
        echo "❌ 请使用 root 权限运行此脚本"
        exit 1
    fi
    
    # 检查图形界面
    if ! check_gui; then
        echo "📦 需要安装图形界面支持..."
        read -p "是否安装图形界面? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            install_gui_support
        fi
    fi
    
    # 配置 Unity
    setup_unity_remote
    
    # 配置本地模式
    setup_local_mode
    
    # 配置 VNC
    setup_vnc
    
    echo ""
    echo "🎉 远程开发环境配置完成！"
    echo ""
    echo "📋 下一步操作:"
    echo "1. 设置 VNC 密码: vncpasswd"
    echo "2. 启动 VNC 服务器: ./start-vnc.sh"
    echo "3. 通过 VNC 连接到远程桌面"
    echo "4. 启动 Unity Hub: /opt/unity/start-unity-hub.sh"
    echo "5. 安装 Unity Editor 和创建项目"
    echo "6. 启动本地模式 MCP: ./start-unity-local.sh"
    echo ""
    echo "🔗 连接信息:"
    echo "   VNC: YOUR_SERVER_IP:5901"
    echo "   MCP: http://localhost:8940/sse (在远程桌面内)"
}

# 运行主程序
main "$@"
