#!/bin/bash

# VS Code Playwright MCP 配置脚本

echo "🚀 开始配置 VS Code Playwright MCP 服务..."

# 检查 VS Code 是否安装
if ! command -v code &> /dev/null; then
    echo "❌ VS Code 未安装或未添加到 PATH"
    echo "请先安装 VS Code 并确保 'code' 命令可用"
    exit 1
fi

echo "✅ VS Code 已安装: $(code --version | head -n1)"

# 检查 Node.js 版本
NODE_VERSION=$(node --version | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" = "$REQUIRED_VERSION" ]; then
    echo "✅ Node.js 版本满足要求: v$NODE_VERSION"
else
    echo "❌ Node.js 版本过低: v$NODE_VERSION (需要 ≥18.0.0)"
    exit 1
fi

# 创建输出目录
mkdir -p playwright-output
echo "✅ 创建输出目录: playwright-output"

# 测试 Playwright MCP 包
echo "🔍 测试 Playwright MCP 包..."
if npx @playwright/mcp@latest --help > /dev/null 2>&1; then
    echo "✅ Playwright MCP 包可用"
else
    echo "⚠️  首次运行可能需要下载包，这是正常的"
fi

# 打开 VS Code 工作区
echo "🎯 打开 VS Code 工作区..."
code playwright-mcp.code-workspace

echo ""
echo "🎉 配置完成！"
echo ""
echo "📋 下一步操作："
echo "1. VS Code 会打开工作区文件"
echo "2. 点击右下角的 'Open Workspace' 按钮"
echo "3. 重启 VS Code"
echo "4. 安装推荐的扩展（GitHub Copilot）"
echo "5. 开始使用 Playwright MCP 功能！"
echo ""
echo "📚 参考文档："
echo "- VS-CODE-SETUP-GUIDE.md - 详细配置指南"
echo "- usage-examples.md - 使用示例"
echo "- README.md - 完整文档"
