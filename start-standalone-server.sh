#!/bin/bash

# Playwright MCP 独立服务器启动脚本

echo "🚀 启动 Playwright MCP 独立服务器..."

# 检查端口是否被占用
PORT=8931
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  端口 $PORT 已被占用，正在尝试终止现有进程..."
    lsof -ti:$PORT | xargs kill -9
    sleep 2
fi

# 创建输出目录
mkdir -p playwright-output

echo "📋 服务器配置："
echo "   - 端口: $PORT"
echo "   - 主机: 0.0.0.0 (允许外部访问)"
echo "   - 浏览器: Chrome (有头模式)"
echo "   - 输出目录: ./playwright-output"
echo "   - SSE 端点: http://localhost:$PORT/sse"

echo ""
echo "🌐 启动服务器..."

# 启动独立服务器
npx @playwright/mcp@latest \
  --port $PORT \
  --host 0.0.0.0 \
  --config standalone-server-config.json \
  --browser chrome \
  --output-dir ./playwright-output \
  --save-trace

echo "🛑 服务器已停止"
