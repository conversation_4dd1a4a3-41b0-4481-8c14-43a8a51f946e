# 🎯 MCP 服务器集合 - 项目总览

## 📋 项目概述

这个项目包含了两个完整的 MCP (Model Context Protocol) 服务器部署：

1. **Microsoft Playwright MCP** - 浏览器自动化服务
2. **Unity MCP** - Unity Editor 集成服务

两个服务都支持独立服务器模式和直接客户端集成模式。

## 🗂️ 项目结构

```
/root/Desktop/MCP/
├── 🎭 Playwright MCP 服务
│   ├── 配置文件
│   │   ├── mcp-config.json                 # 基础配置
│   │   ├── mcp-config-advanced.json        # 高级配置
│   │   ├── standalone-server-config.json   # 独立服务器配置
│   │   └── playwright-mcp-config.json      # 详细配置
│   ├── 启动脚本
│   │   ├── manage-servers.sh               # 服务器管理工具
│   │   ├── start-standalone-server.sh      # 标准模式 (8931)
│   │   ├── start-headless-server.sh        # 无头模式 (8932)
│   │   └── start-vision-server.sh          # 视觉模式 (8933)
│   └── 输出目录
│       └── playwright-output/              # 截图、PDF、跟踪文件
│
├── 🎮 Unity MCP 服务
│   ├── Unity-MCP/                          # 克隆的原始项目
│   │   ├── Server~/                        # Node.js 服务器
│   │   └── Editor/                         # Unity Editor 插件
│   ├── 配置文件
│   │   ├── unity-mcp-config.json           # 服务器配置
│   │   └── unity-client-configs.json       # 客户端配置
│   └── 启动脚本
│       ├── manage-unity-mcp.sh             # Unity 管理工具
│       ├── start-unity-mcp.sh              # STDIO 模式
│       └── start-unity-standalone.sh       # 独立服务器 (8940)
│
├── 📚 文档
│   ├── README.md                           # 主文档
│   ├── STANDALONE-SERVER-GUIDE.md          # 独立服务器指南
│   ├── VS-CODE-SETUP-GUIDE.md             # VS Code 配置
│   ├── CURSOR-TROUBLESHOOTING.md           # Cursor 故障排除
│   ├── UNITY-MCP-DEPLOYMENT.md             # Unity MCP 部署指南
│   ├── CONFIGURATION-SUMMARY.md            # 配置总结
│   └── PROJECT-OVERVIEW.md                 # 本文档
│
├── 🔧 客户端配置
│   ├── client-configs.json                 # 通用客户端配置
│   ├── vscode-settings.json                # VS Code 配置
│   ├── playwright-mcp.code-workspace       # VS Code 工作区
│   └── cursor-quick-fix.md                 # Cursor 快速修复
│
└── 🧪 测试工具
    ├── simple-test.js                      # 简单配置测试
    ├── test-playwright-mcp.js              # Playwright 功能测试
    └── test-mcp-direct.js                  # 直接 MCP 测试
```

## 🚀 服务端口分配

| 服务 | 端口 | 模式 | 用途 |
|------|------|------|------|
| Playwright 标准 | 8931 | 有头浏览器 | 开发调试 |
| Playwright 无头 | 8932 | 无头浏览器 | 生产环境 |
| Playwright 视觉 | 8933 | 截图交互 | 坐标点击 |
| Unity MCP | 8940 | 独立服务器 | Unity 集成 |
| Unity WebSocket | 8090 | Unity Editor | Editor 通信 |

## 🎯 快速启动

### Playwright MCP

```bash
# 启动无头模式（推荐）
./manage-servers.sh start headless

# 查看状态
./manage-servers.sh status

# 客户端配置
{
  "mcpServers": {
    "playwright": {
      "url": "http://localhost:8932/sse"
    }
  }
}
```

### Unity MCP

```bash
# 启动 Unity MCP
./manage-unity-mcp.sh start standalone

# 查看状态
./manage-unity-mcp.sh status

# 客户端配置
{
  "mcpServers": {
    "unity-mcp": {
      "url": "http://localhost:8940/sse"
    }
  }
}
```

## 🔧 支持的客户端

### 已测试客户端

| 客户端 | Playwright MCP | Unity MCP | 配置方式 |
|--------|----------------|-----------|----------|
| VS Code | ✅ | ✅ | 直接集成 / SSE |
| Cursor | ✅ | ✅ | 直接集成 / SSE |
| Claude Desktop | ✅ | ✅ | 直接集成 / SSE |
| Windsurf | ✅ | ✅ | 直接集成 / SSE |

### 配置模式

1. **直接命令模式**：客户端直接运行 MCP 服务器
2. **独立服务器模式**：通过 SSE 端点连接独立服务器

## 🛠️ 功能特性

### Playwright MCP 功能

- 🌐 网页导航和截图
- 🖱️ 元素点击和输入
- 📄 PDF 生成和文件上传
- 🔄 多标签页管理
- 🎯 视觉模式（坐标点击）
- 🧪 测试代码生成

### Unity MCP 功能

- 🎮 Unity Editor 控制
- 🎯 游戏对象操作
- 🔧 组件管理
- 📦 包管理
- 🧪 测试运行
- 📊 资源管理

## 📊 管理工具

### 通用管理

```bash
# Playwright 服务器管理
./manage-servers.sh [start|stop|status|restart] [mode]

# Unity 服务器管理
./manage-unity-mcp.sh [install|start|stop|status|test] [mode]
```

### 状态监控

```bash
# 查看所有服务状态
./manage-servers.sh status
./manage-unity-mcp.sh status

# 测试连接
curl http://localhost:8932/sse  # Playwright
curl http://localhost:8940/health  # Unity
```

## 🚨 故障排除

### 常见问题

1. **端口冲突**：使用 `lsof -i :端口号` 检查占用
2. **权限问题**：确保脚本有执行权限
3. **依赖缺失**：检查 Node.js 版本和包安装
4. **网络问题**：检查防火墙和网络连接

### 调试步骤

1. 检查服务器状态
2. 查看日志文件
3. 测试端点连接
4. 验证客户端配置

## 🎉 使用示例

### Playwright 自动化

```
@assistant 使用 Playwright 帮我：
1. 打开 https://www.github.com
2. 搜索 "MCP servers"
3. 截图保存结果
4. 生成相应的测试代码
```

### Unity 开发

```
@assistant 使用 Unity MCP 帮我：
1. 创建一个新的游戏对象
2. 添加 Rigidbody 组件
3. 设置物理属性
4. 运行相关测试
```

## 📈 扩展性

这个项目架构支持：

- ✅ 添加更多 MCP 服务器
- ✅ 自定义配置和脚本
- ✅ 多客户端同时使用
- ✅ 远程部署和访问
- ✅ 容器化部署

## 🎯 下一步

1. **选择需要的服务**：Playwright、Unity 或两者
2. **启动相应服务器**：使用管理脚本
3. **配置客户端**：添加 MCP 服务器配置
4. **开始使用**：享受强大的自动化功能

现在您拥有了一个功能完整、易于管理的 MCP 服务器集合！🚀
