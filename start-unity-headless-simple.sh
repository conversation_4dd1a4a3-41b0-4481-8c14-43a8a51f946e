#!/bin/bash

# 简化的 Unity Headless 启动脚本

echo "🎮 启动 Unity Headless Mode (简化版)..."

# 1. 检查和启动虚拟显示
start_virtual_display() {
    if ! pgrep -f "Xvfb.*:99" >/dev/null; then
        echo "🖥️ 启动虚拟显示..."
        Xvfb :99 -screen 0 1024x768x24 >/dev/null 2>&1 &
        sleep 2
        echo "✅ 虚拟显示已启动"
    else
        echo "✅ 虚拟显示已运行"
    fi
}

# 2. 使用 Unity Hub 启动 Unity Editor (Headless)
start_unity_with_hub() {
    echo "🎮 使用 Unity Hub 启动 Unity Editor..."
    
    export DISPLAY=:99
    export UNITY_PROJECT_PATH="/root/Desktop/MCP/Unity-Test-Project"
    
    # 检查项目
    if [ ! -d "$UNITY_PROJECT_PATH" ]; then
        echo "❌ Unity 项目不存在: $UNITY_PROJECT_PATH"
        return 1
    fi
    
    # 启动 Unity Hub (后台模式)
    echo "🚀 启动 Unity Hub (后台模式)..."
    
    # 创建临时脚本来启动 Unity
    cat > /tmp/start-unity-headless.sh << EOF
#!/bin/bash
export DISPLAY=:99
cd /opt/unity

# 尝试直接启动 Unity Editor (如果已安装)
if [ -d "\$HOME/Unity/Hub/Editor" ]; then
    UNITY_EDITOR=\$(find "\$HOME/Unity/Hub/Editor" -name "Unity" -type f -executable | head -1)
    if [ -n "\$UNITY_EDITOR" ]; then
        echo "🎯 找到 Unity Editor: \$UNITY_EDITOR"
        "\$UNITY_EDITOR" \\
            -batchmode \\
            -nographics \\
            -projectPath "$UNITY_PROJECT_PATH" \\
            -logFile /tmp/unity-headless.log &
        echo \$! > /tmp/unity-headless.pid
        echo "✅ Unity Headless 已启动"
        exit 0
    fi
fi

# 如果没有找到已安装的 Unity，启动 Unity Hub
echo "🎮 启动 Unity Hub..."
./UnityHub.AppImage --no-sandbox --headless &
UNITY_HUB_PID=\$!
echo \$UNITY_HUB_PID > /tmp/unity-hub.pid

echo "✅ Unity Hub 已启动 (PID: \$UNITY_HUB_PID)"
echo "💡 请通过 Unity Hub 安装 Unity Editor，然后重新运行此脚本"
EOF

    chmod +x /tmp/start-unity-headless.sh
    /tmp/start-unity-headless.sh
}

# 3. 启动 Unity MCP 模拟器
start_unity_mcp() {
    echo "🔧 启动 Unity MCP 环境..."
    
    # 停止现有的模拟器
    pkill -f "unity-simulator" 2>/dev/null
    
    # 启动模拟器
    cd /root/Desktop/MCP
    node unity-simulator.js >/dev/null 2>&1 &
    SIMULATOR_PID=$!
    echo $SIMULATOR_PID > /tmp/unity-simulator.pid
    
    sleep 3
    
    if lsof -i :8090 >/dev/null 2>&1; then
        echo "✅ Unity MCP 模拟器已启动 (端口 8090)"
    else
        echo "❌ Unity MCP 模拟器启动失败"
    fi
}

# 4. 状态检查
check_status() {
    echo ""
    echo "🔍 Unity Headless 状态检查:"
    echo "================================"
    
    # 检查虚拟显示
    if pgrep -f "Xvfb.*:99" >/dev/null; then
        echo "✅ 虚拟显示运行中"
    else
        echo "❌ 虚拟显示未运行"
    fi
    
    # 检查 Unity Editor
    if [ -f /tmp/unity-headless.pid ]; then
        PID=$(cat /tmp/unity-headless.pid)
        if ps -p $PID > /dev/null; then
            echo "✅ Unity Editor 运行中 (PID: $PID)"
        else
            echo "❌ Unity Editor 未运行"
        fi
    else
        echo "❌ Unity Editor 未启动"
    fi
    
    # 检查 Unity Hub
    if [ -f /tmp/unity-hub.pid ]; then
        PID=$(cat /tmp/unity-hub.pid)
        if ps -p $PID > /dev/null; then
            echo "✅ Unity Hub 运行中 (PID: $PID)"
        else
            echo "❌ Unity Hub 未运行"
        fi
    fi
    
    # 检查 Unity MCP
    if lsof -i :8090 >/dev/null 2>&1; then
        echo "✅ Unity MCP 端口 8090 已开放"
    else
        echo "❌ Unity MCP 端口 8090 未开放"
    fi
    
    echo ""
    echo "📋 管理命令:"
    echo "   查看 Unity 日志: tail -f /tmp/unity-headless.log"
    echo "   停止 Unity: kill \$(cat /tmp/unity-headless.pid)"
    echo "   停止 Unity Hub: kill \$(cat /tmp/unity-hub.pid)"
    echo "   停止模拟器: kill \$(cat /tmp/unity-simulator.pid)"
    echo ""
    echo "🎯 Cursor 测试:"
    echo "   @cursor 使用 Unity MCP 获取场景信息"
    echo "   @cursor 使用 Unity MCP 创建游戏对象"
}

# 5. 清理函数
cleanup() {
    echo ""
    echo "🛑 清理 Unity Headless 环境..."
    
    # 停止 Unity Editor
    if [ -f /tmp/unity-headless.pid ]; then
        kill $(cat /tmp/unity-headless.pid) 2>/dev/null
        rm -f /tmp/unity-headless.pid
    fi
    
    # 停止 Unity Hub
    if [ -f /tmp/unity-hub.pid ]; then
        kill $(cat /tmp/unity-hub.pid) 2>/dev/null
        rm -f /tmp/unity-hub.pid
    fi
    
    # 停止模拟器
    if [ -f /tmp/unity-simulator.pid ]; then
        kill $(cat /tmp/unity-simulator.pid) 2>/dev/null
        rm -f /tmp/unity-simulator.pid
    fi
    
    # 停止虚拟显示
    pkill -f "Xvfb.*:99" 2>/dev/null
    
    echo "✅ 清理完成"
}

# 主程序
main() {
    echo "🚀 启动 Unity Headless 环境..."
    
    # 设置清理陷阱
    trap cleanup EXIT
    
    start_virtual_display
    start_unity_with_hub
    start_unity_mcp
    check_status
    
    echo ""
    echo "🎉 Unity Headless 环境已启动！"
    echo ""
    echo "💡 提示:"
    echo "   - Unity 在后台运行，无需图形界面"
    echo "   - 资源占用更少，适合服务器环境"
    echo "   - 支持完整的 Unity MCP 功能"
    echo ""
    echo "按 Ctrl+C 停止所有服务..."
    
    # 保持脚本运行
    while true; do
        sleep 10
        
        # 检查关键进程是否还在运行
        if ! lsof -i :8090 >/dev/null 2>&1; then
            echo "⚠️  Unity MCP 连接丢失，重启模拟器..."
            start_unity_mcp
        fi
    done
}

# 运行主程序
main "$@"
